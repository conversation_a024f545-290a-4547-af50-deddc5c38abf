PODS:
  - camera_avfoundation (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - FlutterMacOS
  - Flutter (1.0.0)
  - flutter_secure_storage (6.0.0):
    - Flutter
  - MTBBarcodeScanner (5.0.11)
  - onesignal_flutter (5.2.6):
    - Flutter
    - OneSignalXCFramework (= 5.2.5)
  - OneSignalXCFramework (5.2.5):
    - OneSignalXCFramework/OneSignalComplete (= 5.2.5)
  - OneSignalXCFramework/OneSignal (5.2.5):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalExtension
    - OneSignalXCFramework/OneSignalLiveActivities
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalComplete (5.2.5):
    - OneSignalXCFramework/OneSignal
    - OneSignalXCFramework/OneSignalInAppMessages
    - OneSignalXCFramework/OneSignalLocation
  - OneSignalXCFramework/OneSignalCore (5.2.5)
  - OneSignalXCFramework/OneSignalExtension (5.2.5):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalInAppMessages (5.2.5):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalLiveActivities (5.2.5):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalLocation (5.2.5):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalNotifications (5.2.5):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalExtension
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalOSCore (5.2.5):
    - OneSignalXCFramework/OneSignalCore
  - OneSignalXCFramework/OneSignalOutcomes (5.2.5):
    - OneSignalXCFramework/OneSignalCore
  - OneSignalXCFramework/OneSignalUser (5.2.5):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - qr_code_scanner (0.2.0):
    - Flutter
    - MTBBarcodeScanner
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/darwin`)
  - Flutter (from `Flutter`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - onesignal_flutter (from `.symlinks/plugins/onesignal_flutter/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - qr_code_scanner (from `.symlinks/plugins/qr_code_scanner/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)

SPEC REPOS:
  trunk:
    - MTBBarcodeScanner
    - OneSignalXCFramework

EXTERNAL SOURCES:
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/darwin"
  Flutter:
    :path: Flutter
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  onesignal_flutter:
    :path: ".symlinks/plugins/onesignal_flutter/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  qr_code_scanner:
    :path: ".symlinks/plugins/qr_code_scanner/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"

SPEC CHECKSUMS:
  camera_avfoundation: dd002b0330f4981e1bbcb46ae9b62829237459a4
  connectivity_plus: 4c41c08fc6d7c91f63bc7aec70ffe3730b04f563
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_secure_storage: d33dac7ae2ea08509be337e775f6b59f1ff45f12
  MTBBarcodeScanner: f453b33c4b7dfe545d8c6484ed744d55671788cb
  onesignal_flutter: f441aaefa112988a1e786c695a30695ad0291763
  OneSignalXCFramework: 76ca508fc2b941317729d13fdec96817f8ef5b3b
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  qr_code_scanner: bb67d64904c3b9658ada8c402e8b4d406d5d796e
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78

PODFILE CHECKSUM: 819463e6a0290f5a72f145ba7cde16e8b6ef0796

COCOAPODS: 1.15.2
