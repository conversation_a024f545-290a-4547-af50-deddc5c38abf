# gym_app

[Figma](https://www.figma.com/design/i1PpbrWS6oGg10hr8tgUet/Gym-App?node-id=1047-525&t=4J3AQEqEw2bXYL3F-0)
- if you want to g new apk for development releases  : `flutter build apk` 
- if you want to g new apk for production releases  : `flutter build apk --split-per-abi` 
(without compile)
[doc](https://docs.flutter.dev/deployment/android#add-a-launcher-icon)
---
### (1) [change app icon](https://www.youtube.com/watch?v=C9yTHKbwKOI&ab_channel=Nerdbash)
---
GymApp
- ar-DZ.json : hebrew 
- ar-AE.json : arabic
--- 
-  intl: ^0.17.0 => date format 
- permission_handler
- camera
- qr_code_scanner
---
- splash screen
- translate
- app.dart
---
| Mission |  Status  |
|---------|----------|
|  connectivity      | TODO       |
|  splash screen     | DONE |
|  routing           | InProgress |
|  shared_preferences| InProgress |
|  Add date under each day of the week| TODO |
|  Add date to field| TODO |

---
### packages :
- easy_localization --> translation
- permission_handler
- camera
- dio --> API
- shared_preferences --> local storage
- intl ----> date format
---
### Booking :
- 0 - white , 1 - green , 2 - white
`import 'dart:developer';`
- log("HI");
---
devtools:
- inspector
- timeline - find the Jank - lag in the app (to see what happened)
- performance view
-------------
### Reading Response 
```dart
var jsonResponse = json.decode(response.toString());
var studentEntryID = jsonResponse['result']['studentEntryID'];
```
-------------
### try parse - check if we can parse
```dart
var value = int.tryParse(qrCodeString);
if (value == null) {

    return;
}
```
### where & contains & toList()
```
List<Message> subjectMatches = _messages
    .where((message) => message.subject.contains(query))
    .toList();
```
### Check Current Lang
```dart
Locale? currentLocal = EasyLocalization.of(context)!.currentLocale;
var failedMessage = '';
if (currentLocal == const Locale('he', 'DZ')) {
    failedMessage = response.data['result']['failedMessage1'];
} else {
    failedMessage = response.data['result']['failedMessage2'];
}
```

### Status
```dart
 var message = response.data['message'];
      if (response.statusCode == 200 && message == 'Succeeded') 
```

### Try/Catch
- always check api request

### Widgets to use 
- CustomErrorDialog

### Switch lang
```dart
                        Text(
                            (EasyLocalization.of(context)!.currentLocale ==
                                    const Locale('he', 'DZ')
                                ? activities[i]['activityName1']
                                : activities[i]['activityName2']),
                            style: const TextStyle(
                                fontSize: 14, color: Colors.white)),
```

---
### Images resource :
- can see image use `flutter clean`
- https://unsplash.com/

