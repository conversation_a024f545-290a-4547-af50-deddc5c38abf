import 'dart:developer';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gym_app/core/constants/color_manager.dart';
import 'package:gym_app/core/constants/strings.dart';
import 'package:gym_app/core/helper/cache_helper.dart';
import 'package:gym_app/core/helper/dialog_helpers.dart';
import 'package:gym_app/core/helper/dio_helper.dart';
import 'package:gym_app/core/widgets/custom_bottom_navigationbar.dart';
import 'package:gym_app/core/widgets/custom_error_dialog.dart';
import 'package:gym_app/core/widgets/custom_popup_menu_button.dart';
import 'package:gym_app/core/widgets/lang_change_button.dart';
import 'package:gym_app/core/widgets/qr_scan_fab.dart';
import 'package:gym_app/features/home/<USER>/home_page.dart';
import 'package:provider/provider.dart';

class BookingPage extends StatefulWidget {
  const BookingPage({super.key});

  @override
  _BookingPageState createState() => _BookingPageState();
}

class _BookingPageState extends State<BookingPage> {
  int _selectedIndex = 1;
  int maxActivities = 0;
  GlobalKey qrKey = GlobalKey();
  String? scanState;
  bool _isLoading = true;
  List<Map<String, List<Schedule>>> weeklySchedules = [{}];
  DateTime currentFromDate = DateTime.now();
  bool hasSubscriptions = true;
  bool _isDialogOpen = false; // Add this flag

  @override
  void initState() {
    super.initState();
    if (CacheHelper.getData(key: 'hasSubscriptions') == true) {
      hasSubscriptions = true;
    } else {
      hasSubscriptions = false;
    }
    DioHelper.init();
    _loadScanState();
    _initializeWeekStart();
    _fetchWeeklyMeetings();
  }

  void _initializeWeekStart() {
    DateTime now = DateTime.now();
    int daysToSubtract = now.weekday % 7;
    currentFromDate = now.subtract(Duration(days: daysToSubtract));
  }

  void _loadScanState() async {
    scanState = CacheHelper.getData(key: 'scanState') as String?;
    scanState ??= 'exit';
    setState(() {});
  }

  void _toggleScanState() async {
    scanState = (scanState == 'entered') ? 'exit' : 'entered';
    await CacheHelper.setData(key: 'scanState', value: scanState);
    setState(() {});
  }

  void _nextWeek() {
    setState(() {
      currentFromDate = currentFromDate.add(const Duration(days: 7));
      _fetchWeeklyMeetings();
    });
  }

  void _previousWeek() {
    setState(() {
      currentFromDate = currentFromDate.subtract(const Duration(days: 7));
      _fetchWeeklyMeetings();
    });
  }

  String _forceEnglishNumerals(String input) {
    return input
        .replaceAll('٠', '0')
        .replaceAll('١', '1')
        .replaceAll('٢', '2')
        .replaceAll('٣', '3')
        .replaceAll('٤', '4')
        .replaceAll('٥', '5')
        .replaceAll('٦', '6')
        .replaceAll('٧', '7')
        .replaceAll('٨', '8')
        .replaceAll('٩', '9');
  }

  String _formatDateWithEnglishNumerals(DateTime date, String format) {
    return _forceEnglishNumerals(DateFormat(format, 'en_US').format(date));
  }

  String _convertArabicToEnglish(String input) {
    return input
        .replaceAll('٠', '0')
        .replaceAll('١', '1')
        .replaceAll('٢', '2')
        .replaceAll('٣', '3')
        .replaceAll('٤', '4')
        .replaceAll('٥', '5')
        .replaceAll('٦', '6')
        .replaceAll('٧', '7')
        .replaceAll('٨', '8')
        .replaceAll('٩', '9');
  }

  Future<void> _fetchWeeklyMeetings() async {
    setState(() {
      _isLoading = true;
    });

    DateTime fromDate = currentFromDate;
    DateTime toDate = fromDate.add(const Duration(days: 6));

    final formattedFromDate = _convertArabicToEnglish(DateFormat('yyyy-MM-dd').format(fromDate));
    final formattedToDate = _convertArabicToEnglish(DateFormat('yyyy-MM-dd').format(toDate));

    try {
      final response = await DioHelper.getData(
        url: '/api/CoursesMeetings/GetStudentWeeklyMeetings',
        query: {
          'fromDate': formattedFromDate,
          'toDate': formattedToDate,
        },
      );

      var message = response.data['message'];
      if (response.statusCode == 200 && message == 'Succeeded') {
        final data = response.data['result'] as List;
        log(data.toString());

        final Map<String, List<Schedule>> weekSchedule = {};

        for (var daySchedule in data) {
          final day = daySchedule['day'] as String;
          final meetings = daySchedule['studentCoursesMeetings'] as List;

          weekSchedule[day] = meetings.map((meeting) {
            return Schedule(
              meeting['courseMeetingID'].toString(),
              meeting['fromTime'],
              meeting['toTime'],
              meeting['courseName'],
              meeting['studentCourseMeetingStatus'],
              meeting['maxMembers'],
              meeting['courseMeetingParticipantsNumber'],
              meeting['description'],
              meeting['courseMeetingDate'],
              meeting['meetingStudentID'],
              meeting['Price'] != null ? meeting['Price'] : 0.0,
            );
          }).toList();

          // log();
          print('&&&&&');
          print(weekSchedule['Wednesday'].toString());
          print('&&&&&');
        }

        setState(() {
          if (weeklySchedules.isNotEmpty) {
            weeklySchedules.removeLast();
          }
          weeklySchedules.add(weekSchedule);
          _calculateMaxActivities();
          _isLoading = false;
        });
      } else {
        Locale? currentLocal = EasyLocalization.of(context)!.currentLocale;
        var failedMessage = '';
        if (currentLocal == const Locale('he', 'DZ')) {
          failedMessage = response.data['result']['failedMessage1'];
        } else {
          failedMessage = response.data['result']['failedMessage2'];
        }
        setState(() {});
        CustomErrorDialog.showErrorDialog(context, failedMessage);
      }
    } catch (e) {
      print('Error fetching weekly meetings: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _calculateMaxActivities() {
    if (weeklySchedules.isEmpty) return;
    maxActivities = weeklySchedules
        .map((week) => week.values.fold(
            0,
            (prev, schedules) =>
                schedules.length > prev ? schedules.length : prev))
        .fold(0, (prev, length) => length > prev ? length : prev);
    maxActivities = maxActivities < 3 ? 3 : maxActivities;
  }

  void _onItemTapped(int index) {
    if (index == _selectedIndex) return;
    setState(() => _selectedIndex = index);
    final routes = ['/home', '', '/articles', '/profile'];
    if (index < routes.length) {
      Navigator.pushReplacementNamed(context, routes[index]);
    }
  }

  String _formatActivityName(String activity) =>
      activity.length > 9 ? '${activity.substring(0, 9)}...' : activity;

  @override
  Widget build(BuildContext context) {
    bool isConnectedToInternet = Provider.of<bool>(context);
    final Map<String, List<Schedule>> currentWeekSchedule =
        weeklySchedules.isNotEmpty ? weeklySchedules.last : {};

    final buttonColor =
        scanState == 'entered' ? Colors.green : ColorManager.myOrangeColor;

    final iconColor = scanState == 'entered' ? Colors.black : Colors.white;
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    // final columnWidth = screenWidth * 0.33;
    final columnWidth = screenWidth * 0.25;
    final cellHeight = screenHeight * 0.090;

    return Scaffold(
      backgroundColor: ColorManager.myBackgroundColor,
      appBar: AppBar(
        iconTheme: const IconThemeData(color: Colors.white),
        // backgroundColor: ColorManager.myOrangeColor,
        backgroundColor: ColorManager.myBackgroundColor,

        title:
            Text('Booking'.tr(), style: const TextStyle(color: Colors.white)),
        actions: [
          if (canShowWidgets)
            Container(
              child: Tooltip(
                message: "Messages".tr(),
                child: GestureDetector(
                  onTap: () {
                    Navigator.pushNamed(context, '/messages');
                  },
                  child: Container(
                    padding: EdgeInsets.all(5),
                    decoration: BoxDecoration(
                      color: Color(0xff323232),
                      borderRadius: BorderRadius.circular(50),
                    ),
                    child: SvgPicture.string(
                      height: 26,
                      Strings.messageIcon,
                      color: ColorManager.myOrangeColor,
                      // color:Colors.orange
                    ),
                  ),
                ),
              ),
            ),
          const SizedBox(width: 15),
          LanguageChangeButton(),
          const SizedBox(width: 10),
          // CustomPopupMenuButton(parentContext: context)
          Container(
            margin: EdgeInsets.fromLTRB(10, 0, 0, 0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(50),
              color: Color(0xff323232),
            ),
            child: CustomPopupMenuButton(parentContext: context),
          )
        ],
      ),
      body: isConnectedToInternet
          ? _isLoading
              ? Center(
                  child: SizedBox(
                  height: 200.0,
                  width: 200.0,
                  child: Container(
                    child:
                        // Image.network(
                        //   'https://media1.giphy.com/media/JO1jH6VJLkPSS7pJTk/giphy.gif?cid=6c09b952k5ozu3sr8pwdoxidy563bfeol1g84zif39030tp8&ep=v1_internal_gif_by_id&rid=giphy.gif&ct=s',
                        //   fit: BoxFit.cover,
                        // ),
                        Image.asset(
                      'assets/images/14_screen_loading.gif',
                      fit: BoxFit.cover,
                    ),
                  ),
                ))
              : SingleChildScrollView(
                  scrollDirection: Axis.vertical,
                  child: Container(
                    color: ColorManager.myBackgroundColor,
                    width: double.infinity,
                    padding: const EdgeInsets.fromLTRB(0, 10.2, 0, 0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildWeekNavigation(),
                        _buildScheduleTable(
                            currentWeekSchedule, columnWidth, cellHeight),
                        const SizedBox(
                          height: 45,
                        )
                      ],
                    ),
                  ),
                )
          : Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.wifi_off, size: 100, color: Colors.red),
                  const SizedBox(height: 20),
                  Text(
                    'ConnectToInternet'.tr(),
                    style: const TextStyle(fontSize: 18, color: Colors.red),
                  ),
                ],
              ),
            ),
      bottomNavigationBar: CustomBottomNavigationBar(
        selectedIndex: _selectedIndex,
        onItemTapped: _onItemTapped,
      ),
      floatingActionButton: QRScanFAB(
        onScanSuccess: (qrCodeString) {
          print('QR Code Scanned in BookingPage: $qrCodeString');
          _toggleScanState();
        },
        qrKey: qrKey,
        buttonColor: isConnectedToInternet && hasSubscriptions
            ? buttonColor
            : Colors.grey,
        iconColor:
            isConnectedToInternet && hasSubscriptions ? iconColor : Colors.grey,
        isEnabled: (isConnectedToInternet && hasSubscriptions),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }

  Widget _buildWeekNavigation() {
    DateTime fromDate = currentFromDate;
    DateTime toDate = fromDate.add(const Duration(days: 6));

    final formattedFromDate = _formatDateWithEnglishNumerals(fromDate, 'dd/MM/yyyy');
    final formattedToDate = _formatDateWithEnglishNumerals(toDate, 'dd/MM/yyyy');

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: _previousWeek),
        Text('$formattedFromDate - $formattedToDate',
            style: const TextStyle(color: Colors.white)),
        IconButton(
            icon: const Icon(Icons.arrow_forward, color: Colors.white),
            onPressed: _nextWeek),
      ],
    );
  }

  Widget _buildScheduleTable111(Map<String, List<Schedule>> currentWeekSchedule,
      double columnWidth, double cellHeight) {
    return Row(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildFixedColumnHeader(columnWidth),
            ..._buildFixedColumnCells(columnWidth, cellHeight),
          ],
        ),
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Column(
              children: [
                _buildTableHeader(columnWidth),
                Table(
                  defaultColumnWidth: FixedColumnWidth(columnWidth),
                  border: TableBorder.symmetric(
                    inside:
                        const BorderSide(color: Color(0xFF9B9B9B), width: 1.0),
                    // outside: BorderSide.none,
                    outside:
                        const BorderSide(color: Color(0xFF9B9B9B), width: 1.0),
                  ),
                  children: _buildTableRows(
                      currentWeekSchedule, columnWidth, cellHeight),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

// add arrow ↓
  Widget _buildScheduleTable(Map<String, List<Schedule>> currentWeekSchedule,
      double columnWidth, double cellHeight) {
    bool showMoreArrow = maxActivities > 3;

    return Stack(
      children: [
        Row(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildFixedColumnHeader(columnWidth),
                ..._buildFixedColumnCells(columnWidth, cellHeight),
              ],
            ),
            Expanded(
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Column(
                  children: [
                    _buildTableHeader(columnWidth),
                    Table(
                      defaultColumnWidth: FixedColumnWidth(columnWidth),
                      border: TableBorder.symmetric(
                        inside: const BorderSide(
                            color: Color(0xFF9B9B9B), width: 1.0),
                        outside: const BorderSide(
                            color: Color(0xFF9B9B9B), width: 1.0),
                      ),
                      children: _buildTableRows(
                          currentWeekSchedule, columnWidth, cellHeight),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        if (showMoreArrow)
          Positioned(
            left: 0,
            top: 0,
            bottom: 0,
            child: Container(
              width: 15,
              color: Colors.black.withOpacity(0.5),
              child: const Center(
                child: Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.red,
                  size: 16,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildFixedColumnHeader(double width) {
    return Container(
      decoration: const BoxDecoration(
          color: ColorManager.myDialogBackgroundColor,
          border: Border(
            bottom: BorderSide(color: Color(0xFF9B9B9B), width: 1.0),
          )),
      width: width,
      // padding: const EdgeInsets.all(5),
      // padding: const EdgeInsets.fromLTRB(4.8, 4.8, 4.8, 4.8),
      padding: const EdgeInsets.fromLTRB(4.8, 5.9, 4.8, 4),
      // color: ColorManager.myDialogBackgroundColor,
      child: Text('Day'.tr(),
          style: const TextStyle(
              color: Colors.white, fontSize: 12, fontWeight: FontWeight.w400)),
    );
  }

  List<Widget> _buildFixedColumnCells(double width, double height) {
    List<Widget> cells = [];
    DateTime now = DateTime.now();

    for (int i = 0; i < daysOfWeek.length; i++) {
      DateTime date = currentFromDate.add(Duration(days: i));
      String formattedDate = _formatDateWithEnglishNumerals(date, 'dd/MM');
      bool isToday = now.year == date.year &&
          now.month == date.month &&
          now.day == date.day;

      cells.add(
        Container(
          decoration: BoxDecoration(
            // color: isToday ? Colors.blueAccent : ColorManager.myDialogBackgroundColor, // Highlight current day
            color: isToday
                ? ColorManager.myOrangeColor
                : ColorManager.myDialogBackgroundColor, // Highlight current day
            border: const Border(
              bottom: BorderSide(color: Color(0xFF9B9B9B), width: 1.0),
            ),
          ),
          width: width,
          height: height,
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Flexible(
                child: Text(
                  daysOfWeek[i].tr(),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    fontStyle: isToday
                        ? FontStyle.italic
                        : FontStyle.normal,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(height: 4.0),
              Flexible(
                child: Text(
                  formattedDate,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: isToday
                        ? FontWeight.bold
                        : FontWeight.w300,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      );
    }
    return cells;
  }

  Widget _buildTableHeader(double columnWidth) {
    return Row(
      children: List.generate(maxActivities, (index) {
        return Container(
          width: columnWidth,
          padding: const EdgeInsets.all(5.0),
          color: ColorManager.myDialogBackgroundColor,
          child: Text('${'Activity'.tr()} ${index + 1}',
              style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w400)),
        );
      }),
    );
  }

  List<TableRow> _buildTableRows(
      Map<String, List<Schedule>> currentWeekSchedule,
      double columnWidth,
      double cellHeight) {
    return daysOfWeek.map((day) {
      List<Widget> cells = [];
      List<Schedule> schedules = currentWeekSchedule[day] ?? [];
      for (int i = 0; i < maxActivities; i++) {
        if (i < schedules.length) {
          cells.add(_buildTableCell(schedules, i, columnWidth, cellHeight));
        } else {
          cells.add(_buildPlaceholderCell(columnWidth, cellHeight));
        }
      }
      return TableRow(
          children: cells.isEmpty
              ? [_buildPlaceholderCell(columnWidth, cellHeight)]
              : cells);
    }).toList();
  }

  Widget _buildTableCell(
      List<Schedule> schedules, int index, double width, double height) {
    if (index < schedules.length) {
      final textColor =
          _getActivityTextColor(schedules[index].studentCourseMeetingStatus);
      return GestureDetector(
        onTap: () => _showDialog(schedules[index]),
        child: Container(
          alignment: Alignment.center,
          padding: const EdgeInsets.all(8),
          height: height,
          color: Colors.transparent,
          child: Text(
            '${schedules[index].toTime} - ${schedules[index].fromTime}\n${_formatActivityName(schedules[index].courseName)}',
            style: TextStyle(
                color: textColor, fontSize: 10.5, fontWeight: FontWeight.w400),
            textAlign: TextAlign.center,
          ),
        ),
      );
    } else {
      return _buildPlaceholderCell(width, height);
    }
  }

  Widget _buildPlaceholderCell(double width, double height) {
    return Container(
      alignment: Alignment.center,
      padding: const EdgeInsets.all(8),
      height: height,
      width: width,
      color: Colors.transparent,
      child: const Text(
        '',
        style: TextStyle(
            color: ColorManager.myDialogBackgroundColor,
            fontSize: 10.5,
            fontWeight: FontWeight.w400),
        textAlign: TextAlign.center,
      ),
    );
  }

  Color _getActivityTextColor(int activityValue) {
    switch (activityValue) {
      case 1:
        return Colors.green;
      case 3:
        return Colors.red;
      case 2:
      default:
        return Colors.white;
    }
  }

  void _showDialog11111111111111(Schedule schedule) async {
    if (_isDialogOpen) return; // Prevent multiple dialogs from opening
    _isDialogOpen = true;

    log(schedule.courseMeetingID.toString());

    // Parse the courseMeetingDate into a DateTime object
    String englishDate = _convertArabicToEnglish(schedule.courseMeetingDate);
    DateTime meetingDate = DateFormat('dd/MM/yyyy', 'en_US').parse(englishDate);
    DateTime now = DateTime.now();

    try {
      final response = await DioHelper.getData(
        url: '/api/CoursesMeetings/Meetings',
        query: {
          'courseMeetingID': schedule.courseMeetingID.toString(),
        },
      );

      var message = response.data['message'];
      if (response.statusCode == 200 && message == 'Succeeded') {
        final meetingDetails = response.data['result'];
        print(meetingDetails);
        var maxMembers = meetingDetails['maxMembers'];
        double price = meetingDetails['price'];
        print('----------');
        print(price);
        print('----------');

        var description = meetingDetails['description'];
        var studentCount = meetingDetails['studentCount'];
        var fromTime = meetingDetails['fromTime'];
        var toTime = meetingDetails['toTime'];
        var courseMeetingDate = meetingDetails['courseMeetingDate'];
        log(courseMeetingDate);
        // Extract hours and minutes for fromTime and toTime
        int fromHours = fromTime['hours'];
        int fromMinutes = fromTime['minutes'];
        int toHours = toTime['hours'];
        int toMinutes = toTime['minutes'];

        // Create new DateTime objects for the meeting times
        DateTime meetingStartDateTime = DateTime(
          meetingDate.year,
          meetingDate.month,
          meetingDate.day,
          fromHours,
          fromMinutes,
        );

        DateTime meetingEndDateTime = DateTime(
          meetingDate.year,
          meetingDate.month,
          meetingDate.day,
          toHours,
          toMinutes,
        );

        // Update the schedule object
        // schedule.price = price;
        schedule.price = meetingDetails['price'] != null
            ? (meetingDetails['price'] is int
                ? meetingDetails['price'].toDouble()
                : meetingDetails['price'])
            : null;

        log(schedule.price.toString());
// if (schedule.price != null && schedule.price! > 0) {
//           print(schedule.price);
//         }
        schedule.maxMembers = maxMembers;
        schedule.description = description;
        schedule.courseMeetingParticipantsNumber = studentCount;
        schedule.fromTime =
            '${fromHours.toString().padLeft(2, '0')}:${fromMinutes.toString().padLeft(2, '0')}';
        schedule.toTime =
            '${toHours.toString().padLeft(2, '0')}:${toMinutes.toString().padLeft(2, '0')}';
        log('From Time: ${schedule.fromTime}');
        log('To Time: ${schedule.toTime}');

        // Check if the meeting date and time is in the past
        if (meetingStartDateTime.isBefore(now)) {
          showInfoDialog(context, schedule);
        } else {
          if (schedule.studentCourseMeetingStatus == 1) {
            showCancellationDialog(context, schedule, _updateUI);
          } else {
            showBookingDialog(context, schedule, _updateUI);
          }
        }
      } else {
        CustomErrorDialog.showErrorDialog(
            context, 'FailedToLoadMeetingDetails'.tr());
      }
    } catch (e) {
      print('Error fetching course meeting details: $e');
      CustomErrorDialog.showErrorDialog(
          context, 'FailedToLoadMeetingDetails'.tr());
    } finally {
      _isDialogOpen = false; // Reset flag after processing is complete
    }
  }

  void _showDialog(Schedule schedule) async {
    if (_isDialogOpen) return; // Prevent multiple dialogs from opening
    _isDialogOpen = true;

    log(schedule.courseMeetingID.toString());

    // Parse the courseMeetingDate into a DateTime object
    String englishDate = _convertArabicToEnglish(schedule.courseMeetingDate);
    DateTime meetingDate = DateFormat('dd/MM/yyyy', 'en_US').parse(englishDate);
    DateTime now = DateTime.now();

    try {
      final response = await DioHelper.getData(
        url: '/api/CoursesMeetings/Meetings',
        query: {
          'courseMeetingID': schedule.courseMeetingID.toString(),
        },
      );

      var message = response.data['message'];
      if (response.statusCode == 200 && message == 'Succeeded') {
        final meetingDetails = response.data['result'];
        print(meetingDetails);
        var maxMembers = meetingDetails['maxMembers'];

        // Safely assign the price, converting to double if needed
        schedule.price = meetingDetails['price'] != null
            ? (meetingDetails['price'] is int
                ? meetingDetails['price'].toDouble()
                : meetingDetails['price'])
            : null;

        log('Price: ${schedule.price.toString()}');

        schedule.maxMembers = maxMembers;
        schedule.description = meetingDetails['description'];
        schedule.courseMeetingParticipantsNumber =
            meetingDetails['studentCount'];

        // Time handling
        int fromHours = meetingDetails['fromTime']['hours'];
        int fromMinutes = meetingDetails['fromTime']['minutes'];
        int toHours = meetingDetails['toTime']['hours'];
        int toMinutes = meetingDetails['toTime']['minutes'];

        schedule.fromTime =
            '${fromHours.toString().padLeft(2, '0')}:${fromMinutes.toString().padLeft(2, '0')}';
        schedule.toTime =
            '${toHours.toString().padLeft(2, '0')}:${toMinutes.toString().padLeft(2, '0')}';
        log('From Time: ${schedule.fromTime}');
        log('To Time: ${schedule.toTime}');

        // Check if the meeting date and time is in the past
        DateTime meetingStartDateTime = DateTime(
          meetingDate.year,
          meetingDate.month,
          meetingDate.day,
          fromHours,
          fromMinutes,
        );

        if (meetingStartDateTime.isBefore(now)) {
          showInfoDialog(context, schedule);
        } else {
          if (schedule.studentCourseMeetingStatus == 1) {
            showCancellationDialog(context, schedule, _updateUI);
          } else {
            showBookingDialog(context, schedule, _updateUI);
          }
        }
      } else {
        CustomErrorDialog.showErrorDialog(
            context, 'Failed to load meeting details.');
      }
    } catch (e) {
      print('Error fetching course meeting details: $e');
      CustomErrorDialog.showErrorDialog(context, 'CheckConnection'.tr());
    } finally {
      _isDialogOpen = false; // Reset flag after processing is complete
    }
  }

  void _updateUI() {
    setState(() {
      _fetchWeeklyMeetings();
    });
  }
}

class Schedule {
  String courseMeetingID;
  String fromTime;
  String toTime;
  String courseName;
  int studentCourseMeetingStatus;
  int maxMembers;
  int courseMeetingParticipantsNumber;
  String description;
  String courseMeetingDate;
  int meetingStudentID;
  double? price;

  Schedule(
    this.courseMeetingID,
    this.fromTime,
    this.toTime,
    this.courseName,
    this.studentCourseMeetingStatus,
    this.maxMembers,
    this.courseMeetingParticipantsNumber,
    this.description,
    this.courseMeetingDate,
    this.meetingStudentID,
    this.price,
  );
}

List<String> daysOfWeek = [
  'Sunday',
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday'
];
