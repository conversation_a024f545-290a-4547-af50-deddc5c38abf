import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:gym_app/core/constants/color_manager.dart';

class CustomAlertDialog extends StatelessWidget {
  final String title;
  final String content;
  final List<Widget> actions;

  const CustomAlertDialog({
    super.key,
    required this.title,
    required this.content,
    required this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
      child: AlertDialog(
        insetPadding: const EdgeInsets.symmetric(horizontal: 30),
        backgroundColor: ColorManager.myDialogBackgroundColor,
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(3),
              margin: const EdgeInsets.fromLTRB(10, 0, 0, 0),
              decoration: BoxDecoration(
                color: ColorManager.myGreenDialogButtonColor,
                borderRadius: BorderRadius.circular(100),
              ),
              child: const Icon(Icons.check),
            ),
            const SizedBox(width: 10),
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 26,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        content: Text(
          content,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w400,
          ),
        ),
        actions: actions,
      ),
    );
  }
}
