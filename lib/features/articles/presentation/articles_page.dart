import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gym_app/core/constants/color_manager.dart';
import 'package:gym_app/core/widgets/custom_bottom_navigationbar.dart';
import 'package:gym_app/core/widgets/localization_checker.dart';
import 'package:gym_app/features/articles/data/articles.dart';
import 'package:dio/dio.dart';
import 'package:gym_app/core/widgets/qr_scan_fab.dart'; // Import the custom widget
import 'package:gym_app/core/helper/cache_helper.dart'; // Import CacheHelper

class ArticlesPage extends StatefulWidget {
  const ArticlesPage({super.key});

  @override
  _ArticlesPageState createState() => _ArticlesPageState();
}

class _ArticlesPageState extends State<ArticlesPage> {
  int _selectedIndex = 2; // Default index for Articles page
  GlobalKey qrKey = GlobalKey(); // Global key for QR code scanner widget
  String? scanState; // Variable to store scan state

  @override
  void initState() {
    super.initState();
    _loadScanState();
  }

  void _loadScanState() async {
    scanState = CacheHelper.getData(key: 'scanState') as String?;
    scanState ??= 'exit';
    setState(() {});
  }

  void _toggleScanState() async {
    scanState = (scanState == 'entered') ? 'exit' : 'entered';
    await CacheHelper.setData(key: 'scanState', value: scanState);
    setState(() {});
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
    switch (index) {
      case 0:
        Navigator.pushReplacementNamed(context, '/home');
        break;
      case 1:
        Navigator.pushReplacementNamed(context, '/booking');
        break;
      case 2:
        // Current page, do nothing
        break;
      case 3:
        Navigator.pushReplacementNamed(context, '/profile');
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    Color buttonColor = scanState == 'entered' ? Colors.green : Colors.orange;
    Color iconColor = scanState == 'entered' ? Colors.black : Colors.white;

    return Scaffold(
      appBar: AppBar(
        iconTheme: const IconThemeData(
          color: Colors.white, // Change your color here
        ),
        backgroundColor: ColorManager.myOrangeColor,
        title:
            Text('Articles'.tr(), style: const TextStyle(color: Colors.white)),
        actions: <Widget>[
          Row(
            children: [
              Container(
                margin: const EdgeInsets.fromLTRB(5, 0, 0, 0),
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(15), // Border radius
                    ),
                  ),
                  onPressed: () {
                    LocalizationCheacker.changeLanguge(context);
                  },
                  child: const Icon(Icons.translate),
                ),
              ),
            ],
          ),
        ],
      ),
      body: Container(
        color: ColorManager.myBackgroundColor,
        child: SingleChildScrollView(
          child: Column(
            children: [
              const SizedBox(
                height: 15,
              ),
              ListView.builder(
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemCount: articles.length,
                itemBuilder: (context, index) {
                  return GestureDetector(
                    onTap: () {
                      _showActivityDetails(
                        context,
                        articles[index]["name"],
                        articles[index]["details"],
                      );
                    },
                    child: Container(
                      color: const Color(0xFF323232),
                      margin: const EdgeInsets.all(10),
                      width: double.infinity,
                      height: 200, // Adjust height as needed
                      child: Card(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10.0),
                        ),
                        elevation: 5,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: ClipRRect(
                                borderRadius: const BorderRadius.vertical(
                                  top: Radius.circular(10),
                                ),
                                child: Container(
                                  width: double.infinity,
                                  height: 226,
                                  decoration: BoxDecoration(
                                    image: DecorationImage(
                                      image: AssetImage(
                                        articles[index]["img_url"] ??
                                            'assets/images/10_placeholder.png',
                                      ),
                                      fit: BoxFit.fill,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            Container(
                              color: const Color(0xFF323232),
                              width: double.infinity,
                              padding: const EdgeInsets.all(8.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    articles[index]["name"] ?? '',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w500,
                                      fontSize: 20,
                                      color: Colors.white,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    articles[index]["description"] ?? '',
                                    style: const TextStyle(
                                      fontSize: 11,
                                      color: Color(0xFFE0E0E0),
                                      fontWeight: FontWeight.w300,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: CustomBottomNavigationBar(
        selectedIndex: _selectedIndex,
        onItemTapped: _onItemTapped,
      ),
      floatingActionButton: QRScanFAB(
        onScanSuccess: (qrCodeString) {
          // Handle the scan success callback
          print('QR Code Scanned in ArticlesPage: $qrCodeString');
          _toggleScanState(); // Update the state after successful scan
        },
        qrKey: qrKey,
        buttonColor: buttonColor,
        iconColor: iconColor,
        isEnabled: true,
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }

  void _showActivityDetails(
      BuildContext context, String? activityName, String? details) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Activity Details'),
          content: Text(
              'Details for ${activityName ?? 'Unknown activity'}: ${details ?? 'No details available'}'),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }
}
