import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:gym_app/core/helper/cache_helper.dart';
import 'package:gym_app/core/helper/dio_helper.dart';

class PostsPage extends StatefulWidget {
  const PostsPage({super.key});

  @override
  _PostsPageState createState() => _PostsPageState();
}

class _PostsPageState extends State<PostsPage> {
  late Future<Response> _response;

  @override
  void initState() {
    super.initState();
    _response = DioHelper.getData(url: "/posts");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Posts"),
      ),
      body: FutureBuilder<Response>(
        future: _response,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text("Error: ${snapshot.error}"));
          } else if (snapshot.hasData) {
            var data = snapshot.data!.data;
            var firstPostUserId = data[1]['userId']; // Access 'userId' from the map
            // Save first post to local storage
            CacheHelper.setData(key: 'first_post_user_id', value: firstPostUserId);
            print(CacheHelper.getData(key: 'first_post_user_id'));
            return ListView.builder(
              itemCount: data.length,
              itemBuilder: (context, index) {
                var post = data[index];
                return ListTile(
                  title: Text(post['title']),
                  subtitle: Text(post['body']),
                );
              },
            );
          } else {
            return const Center(child: Text("No data available"));
          }
        },
      ),
    );
  }
}
