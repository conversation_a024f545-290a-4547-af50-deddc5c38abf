import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gym_app/core/constants/color_manager.dart';
import 'package:gym_app/core/constants/strings.dart';
import 'package:gym_app/core/helper/cache_helper.dart';
import 'package:gym_app/core/widgets/lang_change_button.dart';
import 'package:gym_app/core/widgets/pin_code_widgets.dart';

class AuthCodePage extends StatefulWidget {
  final int studentID;
  final int organizationID;
  final String token;

  const AuthCodePage(
      {super.key,
      required this.studentID,
      required this.organizationID,
      required this.token});

  @override
  _AuthCodePageState createState() => _AuthCodePageState();
}

class _AuthCodePageState extends State<AuthCodePage> {
  final TextEditingController _authCodeController = TextEditingController();
  Timer? _timer;
  int _start = 60;
  bool _isButtonDisabled = false;

  @override
  void initState() {
    super.initState();
    _startTimer();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_start == 0) {
        setState(() {
          timer.cancel();
        });
      } else {
        setState(() {
          _start--;
        });
      }
    });
  }

  Future<void> _verifyCode(BuildContext context) async {
    setState(() {
      _isButtonDisabled = true;
    });

    try {
      Dio dio = Dio();
      dio.options.headers['Authorization'] = 'Bearer ${widget.token}';
      //String ip = Strings.baseUrl;
      String baseUrl = Strings.baseUrl;

      Response response = await dio.put(
        '$baseUrl/api/Authenticate/VerifySmsCodeToStudent',
        data: {
          'SmsCode': _authCodeController.text,
        },
      );
      var jsonResponse = json.decode(response.toString());

      var message = jsonResponse['message'];

      if (response.statusCode == 200 && message == "Succeeded") {
        Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
        CacheHelper.setData(key: "isLoggedIn", value: true);

        var jsonResponse = json.decode(response.toString());
        var tokens = jsonResponse['result']['token'];
        var refreshToken = jsonResponse['result']['refreshToken'];
        var organizationIndex = CacheHelper.getData(key: 'organizationIndex');
        var mobile = jsonResponse['result']['organizations'][organizationIndex]
                ['phoneNumber'] ??
            'No mobile number'; // Extracting mobile number
        CacheHelper.setData(key: "mobile", value: mobile);

        print(mobile);
        CacheHelper.setData(key: "token", value: tokens);
        CacheHelper.setData(key: "refresh_token", value: refreshToken);
      } else {
        log('Failed to verify code: ${response.statusMessage}');
        var failedMessage = jsonResponse['result']['failedMessage1'];
        Locale? currentLocal = EasyLocalization.of(context)!.currentLocale;
        if (currentLocal == const Locale('he', 'DZ')) {
          failedMessage = jsonResponse['result']['failedMessage1'];
        } else {
          failedMessage = jsonResponse['result']['failedMessage2'];
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(failedMessage)),
        );

        setState(() {
          _isButtonDisabled = false;
        });
      }
    } catch (e) {
      log('Error: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('ErrorVerifyingCode'.tr())),
      );
      setState(() {
        _isButtonDisabled = false;
      });
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final double screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        iconTheme: const IconThemeData(
          color: Colors.white, //change your color here
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: const [
          Row(
            children: [
              LanguageChangeButton(hideDecoration: true),
              SizedBox(width: 35),
            ],
          )
        ],
        title: Text(
          'Verification'.tr(),
          style: const TextStyle(color: Colors.white),
        ),
      ),
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus(); // Close the keyboard
        },
        child: SingleChildScrollView(
          child: Container(
            height: screenHeight,
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage('assets/images/07_Group4_start.png'),
                fit: BoxFit.cover,
              ),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color.fromARGB(255, 19, 2, 10),
                  Color.fromARGB(255, 136, 18, 3),
                  Color.fromARGB(255, 19, 2, 10),
                ],
              ),
            ),
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                const SizedBox(height: 50),
                Text(
                  'AuthHeader'.tr(),
                  style: const TextStyle(fontSize: 32, color: Colors.white),
                ),
                const SizedBox(height: 30),
                Text(
                  'AuthText'.tr(),
                  style: const TextStyle(fontSize: 15, color: Colors.white),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 30),
                  child: PinCodeWidget(
                    controller: _authCodeController,
                  ),
                ),
                Container(
                  width: double.infinity,
                  margin: const EdgeInsets.fromLTRB(30, 0, 30, 0),
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.fromLTRB(10, 5, 10, 5),
                      backgroundColor: ColorManager.myOrangeColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15),
                      ),
                    ),
                    onPressed: _isButtonDisabled || _start <= 0
                        ? null
                        : () => {
                              if ((_authCodeController.text.length == 6))
                                {
                                  _verifyCode(context),
                                }
                              else
                                {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                        content:
                                            Text('ErrorVerifyingCode'.tr())),
                                  )
                                }
                            },
                    child: Text(
                      'AuthCodeButton'.tr(),
                      style: const TextStyle(fontSize: 24),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  '${'EnterYourPhoneCode'.tr()} $_start',
                  style: const TextStyle(color: Colors.white),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
