import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gym_app/core/constants/color_manager.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gym_app/core/constants/my_colors.dart';
import 'package:gym_app/core/helper/cache_helper.dart';
import 'package:gym_app/core/helper/dio_helper.dart';
import 'package:gym_app/core/services/one_signal_service.dart';
import 'package:gym_app/core/widgets/lang_change_button.dart';
import 'dart:developer';
import 'package:gym_app/core/widgets/localization_checker.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  _LoginPageState createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final TextEditingController _idController = TextEditingController();
  bool _isButtonDisabled = true;

  @override
  void initState() {
    super.initState();
    _idController.addListener(_onTextChanged);
    _checkTextField();
  }

  @override
  void dispose() {
    _idController.removeListener(_onTextChanged);
    _idController.dispose();
    super.dispose();
  }

  void _checkTextField() {
    // Check the initial value of the text field and update button state
    if (_idController.text.isNotEmpty) {
      setState(() {
        _isButtonDisabled = false;
      });
    }
  }

  void _onTextChanged() {
    setState(() {
      _isButtonDisabled = _idController.text.length != 9;
    });
  }

  void _navigateToOrganizationPage(
      BuildContext context, List<dynamic> organizations, String token) {
    Navigator.pushNamed(
      context,
      '/organization',
      arguments: {'organizations': organizations, 'token': token},
    ).then((_) {
      _resetButtonState();
    });
  }

  void _resetButtonState() {
    setState(() {
      if (_idController.text.isNotEmpty) {
        _isButtonDisabled = false;
      } else {
        _isButtonDisabled = true;
      }
    });
  }

  Future<void> _authenticate(BuildContext context) async {
    // Close the keyboard
    FocusScope.of(context).unfocus();

    setState(() {
      _isButtonDisabled = true;
    });

    try {
      Response response = await DioHelper.postData(
        url: "/api/Authenticate/AuthenticateIdentityNumber",
        data: {
          "IdentityNumber": _idController.text,
        },
      );
      log(_idController.text);
      var message = response.data['message'];
      if (response.statusCode == 200 && message == 'Succeeded') {
        Map<String, dynamic> responseData = json.decode(response.toString());
        List<dynamic> organizations = responseData['result']['organizations'];
        String token = responseData['result']['token'];
        String fullName = responseData['result']['firstName'] +
            ' ' +
            responseData['result']['lastName'];
        await CacheHelper.setData(key: 'fullName', value: fullName);
        await CacheHelper.setData(key: 'token', value: token);
        if (OneSignalNotificationService.getUserId().isNotEmpty) {
          await DioHelper.storeStudentPlayerIDInDB(
              OneSignalNotificationService.getUserId());
        }
        _navigateToOrganizationPage(context, organizations, token);
      } else {
        // "message": "Failed"
        Locale? currentLocal = EasyLocalization.of(context)!.currentLocale;
        var failedMessage = '';
        if (currentLocal == const Locale('he', 'DZ')) {
          failedMessage = response.data['result']['failedMessage1'];
        } else {
          failedMessage = response.data['result']['failedMessage2'];
        }
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(failedMessage)),
        );
        print("Authentication failed");
        setState(() {
          _isButtonDisabled = false;
        });
      }
    } catch (e) {
      print("Error: $e");
      setState(() {
        _isButtonDisabled = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final double screenHeight = MediaQuery.of(context).size.height;

    return WillPopScope(
      onWillPop: () async {
        _resetButtonState();
        return true;
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        resizeToAvoidBottomInset: false,
        appBar: AppBar(
          iconTheme: const IconThemeData(
            color: Colors.white, //change your color here
          ),
          backgroundColor: Colors.transparent,
          elevation: 0,
          actions: const [
            Row(
              children: [
                LanguageChangeButton(hideDecoration: true),
                SizedBox(width: 35),
              ],
            )
          ],
          // title: Text(
          //   'Gym'.tr(),
          //   style: const TextStyle(color: Colors.white),
          // ),
        ),
        body: GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus(); // Close the keyboard
          },
          child: Container(
            height: screenHeight,
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage('assets/images/07_Group4_start.png'),
                fit: BoxFit.cover,
              ),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color.fromARGB(255, 19, 2, 10),
                  Color.fromARGB(255, 136, 18, 3),
                  Color.fromARGB(255, 19, 2, 10),
                ],
              ),
            ),
            child: SingleChildScrollView(
              child: Container(
                height: screenHeight,
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    const SizedBox(height: 50),
                    Container(
                      margin: const EdgeInsets.fromLTRB(150, 0, 0, 0),
                      child: Text(
                        'LoginHeader'.tr(),
                        style: const TextStyle(
                            fontSize: 30,
                            color: Colors.white,
                            fontFamily: 'Cairo'),
                      ),
                    ),
                    const SizedBox(height: 20),
                    Text("Id".tr(),
                        style:
                            const TextStyle(color: Colors.white, fontSize: 20)),
                    const SizedBox(height: 10),

                    TextFormField(
                      style: const TextStyle(color: Colors.white),
                      controller: _idController,
                      cursorColor: Colors.white,
                      maxLength: 9,
                      decoration: const InputDecoration(
                        prefixIcon: Padding(
                          padding: EdgeInsets.all(0.0),
                          child: Icon(
                            Icons.person,
                            color: Colors.white,
                          ),
                        ),
                        hintStyle: TextStyle(color: Colors.white, fontSize: 27),
                        hintText: '- - - - - - - - -',
                        border: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.grey),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: MyColors.myDarkGray),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.grey),
                        ),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    ),

                    const SizedBox(height: 30),
                    Container(
                      width: double.infinity,
                      margin: const EdgeInsets.fromLTRB(30, 0, 30, 0),
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.fromLTRB(10, 5, 10, 5),
                          backgroundColor: ColorManager.myOrangeColor,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15),
                          ),
                        ),
                        onPressed: _isButtonDisabled
                            ? null
                            : () => _authenticate(context),
                        child: Text(
                          'LoginButton'.tr(),
                          style: const TextStyle(fontSize: 24),
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
