import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gym_app/core/constants/color_manager.dart';
import 'package:gym_app/core/constants/strings.dart';
import 'package:gym_app/core/helper/cache_helper.dart';
import 'package:gym_app/core/widgets/lang_change_button.dart';
import 'dart:developer';

class OrganizationPage extends StatefulWidget {
  final List<dynamic> organizations;
  final String token;

  const OrganizationPage(
      {super.key, required this.organizations, required this.token});

  @override
  State<OrganizationPage> createState() => _OrganizationPageState();
}

class _OrganizationPageState extends State<OrganizationPage> {
  int? _selectedOrganizationID;
  bool _buttonClicked = false;

  late Dio dio;

  @override
  void initState() {
    //String ip = Strings.baseUrl;
    String baseUrl = Strings.baseUrl;
    super.initState();
    dio = Dio(
      BaseOptions(
        //baseUrl: "http://$ip",
        baseUrl: baseUrl,
        receiveDataWhenStatusError: true,
      ),
    );

    // Automatically click the button if there's only one organization
    if (widget.organizations.length == 1) {
      final organization = widget.organizations.first;
      Future.delayed(Duration.zero, () {
        _handleButtonPress(
          context,
          organization['studentID'],
          organization['organizationID'],
          organization['organizationName'],
        );
      });
    }
  }

  Future<void> sendSmsToStudent(BuildContext context, int studentID,
      int organizationID, String token) async {
    try {
      Response response = await dio.put(
        '/api/Authenticate/SendSmsToStudent',
        data: {
          'OrganizationID': organizationID,
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
          },
        ),
      );
      if (response.statusCode == 200) {
        log('SMS sent successfully');
        var jsonResponse = json.decode(response.toString());
        var tokens = jsonResponse['result']['token']; // Extracting the token from the response

        debugPrint('SMS sent successfully');
        debugPrint('Token: $tokens'); // Printing token for verification
        CacheHelper.setData(key: 'token', value: tokens);
        await CacheHelper.setData(key: 'orginizationID', value: organizationID);
        print(response);
        // Navigate to AuthCodePage
        Navigator.pushNamed(
          context,
          '/auth',
          arguments: {
            'studentID': studentID,
            'organizationID': organizationID,
            'token': tokens,
          },
        ).then((_) {
          _resetButtonState();
        });
      } else {
        log('Failed to send SMS: ${response.statusMessage}');
        setState(() {
          _buttonClicked = false;
        });
      }
    } catch (e) {
      log('Error: $e');
      setState(() {
        _buttonClicked = false;
      });
    }
  }

  void _handleButtonPress(
      BuildContext context, int studentID, int organizationID, String organizationName) {
    setState(() {
      _selectedOrganizationID = organizationID;
      _buttonClicked = true;
      CacheHelper.setData(key: 'organization', value: organizationName);
    });
    log('StudentID: $studentID');
    sendSmsToStudent(context, studentID, organizationID, widget.token);
  }

  void _resetButtonState() {
    setState(() {
      _buttonClicked = false;
      _selectedOrganizationID = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double buttonWidth = screenWidth * 0.8; // Fixed width for buttons

    return WillPopScope(
      onWillPop: () async {
        _resetButtonState();
        return true;
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        resizeToAvoidBottomInset: false,
        appBar: AppBar(
          iconTheme: const IconThemeData(
            color: Colors.white,
          ),
          backgroundColor: Colors.transparent,
          elevation: 0,
          actions: const [
            Row(
              children: [
                LanguageChangeButton(hideDecoration: true),
                SizedBox(width: 35),
              ],
            )
          ],
          title: Text(
            'Gym'.tr(),
            style: const TextStyle(color: Colors.white),
          ),
        ),

        // body: Container(
        //   decoration: const BoxDecoration(
        //     image: DecorationImage(
        //       image: AssetImage('assets/images/07_Group4_start.png'),
        //       fit: BoxFit.cover,
        //     ),
        //     gradient: LinearGradient(
        //       begin: Alignment.topLeft,
        //       end: Alignment.bottomRight,
        //       colors: [
        //         Color.fromARGB(255, 19, 2, 10),
        //         Color.fromARGB(255, 136, 18, 3),
        //         Color.fromARGB(255, 19, 2, 10),
        //       ],
        //     ),
        //   ),
        //   child: Center(
        //     child: SingleChildScrollView(
        //       child: Column(
        //         mainAxisAlignment: MainAxisAlignment.center,
        //         children: [
        //           Padding(
        //             padding: const EdgeInsets.symmetric(vertical: 20),
        //             child: Text(
        //               'SelectYourOrganization'.tr(),
        //               style: const TextStyle(
        //                 fontSize: 28,
        //                 fontWeight: FontWeight.bold,
        //                 color: Colors.white,
        //               ),
        //             ),
        //           ),
        //           ...widget.organizations.map((organization) => Column(
        //                 children: [
        //                   buildButton(
        //                     context,
        //                     organization['organizationName'],
        //                     buttonWidth,
        //                     organization['studentID'],
        //                     organization['organizationID'],
        //                   ),
        //                   const SizedBox(height: 25),
        //                 ],
        //               )),
        //         ],
        //       ),
        //     ),
        //   ),
        // ),
body: Container(
  decoration: const BoxDecoration(
    image: DecorationImage(
      image: AssetImage('assets/images/07_Group4_start.png'),
      fit: BoxFit.cover,
    ),
    gradient: LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        Color.fromARGB(255, 19, 2, 10),
        Color.fromARGB(255, 136, 18, 3),
        Color.fromARGB(255, 19, 2, 10),
      ],
    ),
  ),
  child: Center(
    child: SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: Text(
              'SelectYourOrganization'.tr(),
              style: const TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          ...widget.organizations.asMap().entries.map((entry) {
            int index = entry.key;
            var organization = entry.value;
            return Column(
              children: [
                buildButton(
                  context,
                  organization['organizationName'],
                  buttonWidth,
                  organization['studentID'],
                  organization['organizationID'],
                  index,  // Pass the index to buildButton
                ),
                const SizedBox(height: 25),
              ],
            );
          }).toList(),
        ],
      ),
    ),
  ),
),
      
      
      ),
    );
  }

  Widget buildButton(BuildContext context, String text, double width,
      int studentID, int organizationID,int index) {
    bool isSelected = _selectedOrganizationID == organizationID;
    CacheHelper.setData(key: 'organizationIndex',value:index);
    return SizedBox(
      width: width,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 5),
          backgroundColor: isSelected ? Colors.grey : const Color(0xFFFF5534),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
        ),
        onPressed: _buttonClicked
            ? null
            : () => _handleButtonPress(context, studentID, organizationID, text),
        child: Text(
          text,
          style: const TextStyle(fontSize: 24),
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }
}