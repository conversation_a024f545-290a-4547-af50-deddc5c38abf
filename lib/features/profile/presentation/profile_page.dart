import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gym_app/core/constants/color_manager.dart';
import 'package:gym_app/core/helper/cache_helper.dart';
import 'package:gym_app/core/widgets/lang_change_button.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  _ProfilePageState createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  GlobalKey qrKey = GlobalKey(); // Global key for QR code scanner widget
  String? scanState; // Variable to store scan state

  @override
  void initState() {
    super.initState();
    _loadScanState();
  }

  void _loadScanState() async {
    scanState = CacheHelper.getData(key: 'scanState') as String?;
    scanState ??= 'exit';
    setState(() {});
  }

  void _toggleScanState() async {
    scanState = (scanState == 'entered') ? 'exit' : 'entered';
    await CacheHelper.setData(key: 'scanState', value: scanState);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: const IconThemeData(
          color: Colors.white, //change your color here
        ),
        backgroundColor: ColorManager.myBackgroundColor,
        title: Text(
          'Profile'.tr(),
          style: const TextStyle(color: Colors.white),
        ),
        actions: <Widget>[
          Row(
            children: [
              LanguageChangeButton(),
              const SizedBox(width: 35),
            ],
          ),
        ],
      ),
      body: Container(
        color: ColorManager.myBackgroundColor,
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            const Center(
              child: CircleAvatar(
                backgroundColor: Color.fromARGB(255, 255, 255, 255),
                radius: 50,
                backgroundImage: AssetImage('assets/images/profile_pic.png'),
              ),
            ),
            const SizedBox(height: 20),
            Center(
              child: Text(
                '${CacheHelper.getData(key: 'fullName')}',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 10),
            Center(
              child: Text(
                '${CacheHelper.getData(key: 'mobile')}',
                style: const TextStyle(fontSize: 18, color: Colors.white),
              ),
            ),
            const SizedBox(height: 40),
            Center(
              child: Text(
                ' ${'CurrentState'.tr()}: ${scanState?.tr()}',
                style: TextStyle(
                    fontSize: 28,
                    color: scanState == 'exit' ? Colors.grey : Colors.green),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
