// lib/features/splash/presentation/splash_screen.dart
import 'package:flutter/material.dart';
import 'package:gym_app/core/helper/cache_helper.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _loadResources();
  }

  Future<void> _loadResources() async {
    await Future.delayed(const Duration(seconds: 2)); // Simulate loading time

    // Check if the user is logged in or any other initialization logic
    bool isLoggedIn = CacheHelper.getData(key: 'isLoggedIn') ?? false;

    // Navigate to the appropriate page
    if (isLoggedIn) {
      Navigator.of(context).pushReplacementNamed('/home');
    } else {
      Navigator.of(context).pushReplacementNamed('/login');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Container(
          child:
              Image.asset(
            'assets/images/14_screen_loading.gif',
            fit: BoxFit.cover,
          ),
        ),
      ),
    );
  }
}
