import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gym_app/core/constants/color_manager.dart';
import 'package:gym_app/core/helper/dio_helper.dart';
import 'package:gym_app/core/widgets/custom_error_dialog.dart';
import 'package:gym_app/core/widgets/lang_change_button.dart';

class Message {
  final String title;
  final String content;
  final String sendingDate;
  final String sendingTime;

  Message({
    required this.title,
    required this.content,
    required this.sendingDate,
    required this.sendingTime,
  });
}

class MessagesPage extends StatefulWidget {
  const MessagesPage({super.key});

  @override
  _MessagesPageState createState() => _MessagesPageState();
}

class _MessagesPageState extends State<MessagesPage> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  List<Message> _messages = [];
  List<Message> _filteredMessages = [];
  bool _isLoadingStudentMessages =
      true; // Add loading state for student messages

  @override
  void initState() {
    super.initState();
    _getStudentMessages();

    _searchFocusNode.addListener(() {
      if (!_searchFocusNode.hasFocus) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _filterMessages(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredMessages = _messages;
      } else {
        List<Message> titleMatches = _messages
            .where((message) => message.title.contains(query))
            .toList();

        List<Message> contentMatches = _messages
            .where((message) =>
                !titleMatches.contains(message) &&
                message.content.contains(query))
            .toList();

        _filteredMessages = [...titleMatches, ...contentMatches];
      }
    });
  }

  Future<bool> _onWillPop() async {
    if (_searchFocusNode.hasFocus) {
      _searchFocusNode.unfocus();
      return Future.value(false); // Prevent back navigation if keyboard is open
    }
    return Future.value(true); // Allow back navigation if keyboard is not open
  }

  Future<void> _getStudentMessages() async {
    setState(() {
      _isLoadingStudentMessages = true; // Set loading state to true
    });

    try {
      final response = await DioHelper.getData(
        url: '/api/Students/GetStudentMessages',
      );
      var message = response.data['message'];
      if (response.statusCode == 200 && message == 'Succeeded') {
        final data = response.data['result'] as List;
        setState(() {
          _messages = data
              .map((item) => Message(
                  title: item['title'],
                  content: item['content'],
                  sendingDate: item['sendingDate'],
                  sendingTime: item['sendingTime']))
              .toList();
          _filteredMessages = _messages;
          _isLoadingStudentMessages = false; // Set loading state to false
        });
      } else {
        Locale? currentLocal = EasyLocalization.of(context)!.currentLocale;
        var failedMessage = '';
        if (currentLocal == const Locale('he', 'DZ')) {
          failedMessage = response.data['result']['failedMessage1'];
        } else {
          failedMessage = response.data['result']['failedMessage2'];
        }
        CustomErrorDialog.showErrorDialog(context, failedMessage);
      }
    } catch (e) {
      CustomErrorDialog.showErrorDialog(context, 'CheckConnection'.tr());
      setState(() {
        _isLoadingStudentMessages = false; // Set loading state to false
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        backgroundColor: ColorManager.myBackgroundColor,
        resizeToAvoidBottomInset: false,
        appBar: AppBar(
          iconTheme: const IconThemeData(
            color: Colors.white,
          ),
          // backgroundColor: ColorManager.myOrangeColor,
          backgroundColor: ColorManager.myBackgroundColor,
          title: Text(
            'Messages'.tr(),
            style: const TextStyle(color: Colors.white),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () async {
              if (_searchFocusNode.hasFocus) {
                _searchFocusNode.unfocus();
              } else {
                Navigator.of(context).pop();
              }
            },
          ),
          actions: const <Widget>[
            Row(
              children: [
                LanguageChangeButton(),
                SizedBox(width: 10),
              ],
            ),
          ],
        ),
        body: _isLoadingStudentMessages
            ? Center(
                child: SizedBox(
                  height: 200.0,
                  width: 200.0,
                  child: Container(
                    child: Image.asset(
                      'assets/images/14_screen_loading.gif',
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              )
            : GestureDetector(
                onTap: () {
                  FocusScope.of(context)
                      .unfocus(); // Dismiss the keyboard when tapping outside
                },
                child: Container(
                  color: ColorManager.myBackgroundColor,
                  padding: const EdgeInsets.all(16.0),
                  width: double.infinity,
                  child: Column(
                    children: <Widget>[
                      TextField(
                        controller: _searchController,
                        focusNode: _searchFocusNode,
                        decoration: InputDecoration(
                          labelText: 'Search'.tr(),
                          prefixIcon:
                              const Icon(Icons.search, color: Colors.white),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(15.0),
                            borderSide: const BorderSide(
                                color: ColorManager.myOrangeColor),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(15.0),
                            borderSide: const BorderSide(
                                color: ColorManager.myOrangeColor),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(15.0),
                            borderSide: const BorderSide(
                                color: ColorManager.myOrangeColor),
                          ),
                          labelStyle: const TextStyle(color: Colors.white),
                        ),
                        style: const TextStyle(color: Colors.white),
                        cursorColor: Colors.white,
                        onChanged: (query) => _filterMessages(query),
                      ),
                      const SizedBox(height: 20),
                      _filteredMessages.isEmpty
                          ? Center(
                              child: Text(
                                "NoMessagesFound".tr(),
                                style: TextStyle(color: Colors.grey),
                              ),
                            )
                          : Expanded(
                              child: ListView.builder(
                                itemCount: _filteredMessages.length,
                                itemBuilder: (context, index) {
                                  return Container(
                                    margin:
                                        const EdgeInsets.fromLTRB(0, 0, 0, 10),
                                    decoration: const BoxDecoration(
                                      border: Border(
                                        bottom: BorderSide(
                                          // color: ColorManager.myOrangeColor,
                                          color: Colors.grey,
                                          width: 1.0,
                                        ),
                                      ),
                                    ),
                                    child: ListTile(
                                      leading: const Icon(Icons.message,
                                          color: ColorManager.myOrangeColor),
                                      title: Text(
                                        _filteredMessages[index].title,
                                        style: const TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold),
                                      ),
                                      subtitle: Row(
                                        children: [
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  _filteredMessages[index]
                                                      .content,
                                                  style: const TextStyle(
                                                      color: Colors.white),
                                                ),
                                                const SizedBox(height: 5),
                                              ],
                                            ),
                                          ),
                                          Container(
                                            margin: const EdgeInsets.fromLTRB(
                                                0, 0, 10, 0),
                                            child: Column(
                                              // crossAxisAlignment: CrossAxisAlignment.end,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Text(
                                                  _filteredMessages[index]
                                                      .sendingTime,
                                                  style: const TextStyle(
                                                      fontSize: 11,
                                                      color: Colors.white),
                                                ),
                                                Text(
                                                  _filteredMessages[index]
                                                      .sendingDate,
                                                  style: const TextStyle(
                                                      fontSize: 11,
                                                      color: Colors.grey),
                                                ),
                                              ],
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                    ],
                  ),
                ),
              ),
      ),
    );
  }
}
