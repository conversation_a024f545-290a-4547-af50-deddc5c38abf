// activity_widgets.dart
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';

Widget buildActivityHeader(BuildContext context) {
  return Container(
    margin: const EdgeInsets.symmetric(horizontal: 7),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Container(
          margin: const EdgeInsets.fromLTRB(0, 0, 10, 0),
          child: Text(
            'MyActivity'.tr(),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      ],
    ),
  );
}

Widget buildActivityListView(BuildContext context, List activities, ScrollController activityScrollController) {
  if (activities.isEmpty) {
    return Container(
      margin: const EdgeInsets.fromLTRB(0, 0, 30, 0),
      height: 145,
      child: Center(
        child: Text(
          'NoActivityHistoryYet'.tr(),
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  return Container(
    margin: const EdgeInsets.fromLTRB(0, 0, 20, 0),
    height: 145,
    child: Scrollbar(
      thumbVisibility: true,
      controller: activityScrollController,
      child: ListView.builder(
        controller: activityScrollController,
        itemCount: activities.length,
        scrollDirection: Axis.horizontal,
        itemBuilder: (context, i) {
          double itemWidth = activities.length == 1
              ? MediaQuery.of(context).size.width - 40
              : 180;

          return Container(
            padding: const EdgeInsets.all(3),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
            ),
            margin: const EdgeInsets.fromLTRB(10, 0, 10, 5),
            width: itemWidth,
            height: 112.5,
            child: Container(
              padding: const EdgeInsets.all(5),
              decoration: BoxDecoration(
                color: Colors.grey[800],
                borderRadius: BorderRadius.circular(15),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    activities[i]['studentActivityTypeNumber'] == 1
                        ? 'PresenceActivity'.tr()
                        : 'MeetingActivity'.tr() + " " + (EasyLocalization.of(context)!.currentLocale == const Locale('he', 'DZ')
                        ? activities[i]['activityName1']
                        : activities[i]['activityName2']),
                    style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold),
                  ),
                  const Divider(),
                  Row(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          color: const Color.fromARGB(255, 126, 125, 125),
                        ),
                        child: Icon(
                          activities[i]['studentActivityTypeNumber'] == 1
                              ? Icons.history_outlined
                              : Icons.sports_football,
                        ),
                      ),
                      const SizedBox(width: 20),
                      Text(activities[i]['activityDate'],
                          style: const TextStyle(
                              fontSize: 14, color: Colors.white)),
                    ],
                  ),
                  const SizedBox(height: 5),
                  Row(
                    children: [
                      Column(
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.arrow_forward, color: Colors.green),
                              Text(" " + activities[i]['fromTime'],
                                  style: const TextStyle(
                                      fontSize: 12, color: Colors.white)),
                            ],
                          ),
                          Text("Entrance".tr(),
                              style: const TextStyle(
                                  fontSize: 10, color: Colors.green))
                        ],
                      ),
                      const Spacer(),
                      Column(
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.arrow_back, color: Colors.red),
                              Text(" " + activities[i]['toTime'],
                                  style: const TextStyle(
                                      fontSize: 12, color: Colors.white)),
                            ],
                          ),
                          Text("Exit".tr(),
                              style: const TextStyle(
                                  fontSize: 10, color: Colors.red))
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    ),
  );
}
