import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gym_app/core/constants/color_manager.dart';
import 'package:gym_app/core/constants/my_colors.dart';
import 'package:gym_app/core/helper/dio_helper.dart';
import 'package:gym_app/core/widgets/custom_error_dialog.dart';

class Message {
  final String? title;
  final String? content;
  final String? sendingDate;
  final String? sendingTime;

  Message({
    this.title,
    this.content,
    this.sendingDate,
    this.sendingTime,
  });
}

class LastMessagesWidget extends StatefulWidget {
  @override
  _LastMessagesWidgetState createState() => _LastMessagesWidgetState();
}

class _LastMessagesWidgetState extends State<LastMessagesWidget> {
  bool _isLoadingStudentMessages = false;
  List<Message> _messages = [];
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _getStudentMessages();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _getStudentMessages() async {
    setState(() {
      _isLoadingStudentMessages = true;
    });

    try {
      final response = await DioHelper.getData(
        url: '/api/Students/GetLastStudentMessages',
      );
      var message = response.data['message'];
      log(message.toString());
      if (response.statusCode == 200 && message == 'Succeeded') {
        final data = response.data['result'] as List;
        setState(() {
          _messages = data
              .map((item) => Message(
                  title: item['title'],
                  content: item['content'],
                  sendingDate: item['sendingDate'],
                  sendingTime: item['sendingTime']))
              .toList();
        });
      } else {
        Locale? currentLocal = EasyLocalization.of(context)!.currentLocale;
        var failedMessage = '';
        if (currentLocal == const Locale('he', 'DZ')) {
          failedMessage = response.data['result']['failedMessage1'];
        } else {
          failedMessage = response.data['result']['failedMessage2'];
        }
        CustomErrorDialog.showErrorDialog(context, failedMessage);
      }
    } catch (e) {
      CustomErrorDialog.showErrorDialog(context, 'CheckConnection'.tr());
    } finally {
      setState(() {
        _isLoadingStudentMessages = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 7),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                margin: const EdgeInsets.fromLTRB(0, 0, 10, 0),
                child: Text(
                  'LastMessages'.tr(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
              GestureDetector(
                onTap: () {
                  Navigator.pushNamed(context, '/messages');
                },
                child: Text(
                  "ShowAll".tr(),
                  style: TextStyle(color: ColorManager.myOrangeColor),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 15),
        _isLoadingStudentMessages
            ? Center(
                child: CircularProgressIndicator(
                  valueColor:
                      AlwaysStoppedAnimation<Color>(ColorManager.myOrangeColor),
                ),
              )
            : Container(
              margin: EdgeInsets.fromLTRB(0, 0, 15, 0),
                height: 145,
                child: _messages.isNotEmpty
                    ? Scrollbar(
                        thumbVisibility: true,
                        controller: _scrollController,
                        child: ListView.builder(
                          controller: _scrollController,
                          itemCount: _messages.length,
                          scrollDirection: Axis.horizontal,
                          itemBuilder: (context, index) {
                            final message = _messages[index];
                            double itemWidth = _messages.length == 1
                                ? MediaQuery.of(context).size.width - 40
                                : 180;

                            return Container(
                              margin: const EdgeInsets.fromLTRB(10, 0, 10, 5),
                              child: MessageCardWidget(
                                message: message,
                                itemWidth: itemWidth,
                              ),
                            );
                          },
                        ),
                      )
                    : Center(
                        child: Text(
                          'NoMessagesFound'.tr(),
                          style: const TextStyle(
                            color: Colors.grey,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
              ),
      ],
    );
  }
}

class MessageCardWidget extends StatefulWidget {
  final Message message;
  final double itemWidth;

  const MessageCardWidget({
    Key? key,
    required this.message,
    required this.itemWidth,
  }) : super(key: key);

  @override
  _MessageCardWidgetState createState() => _MessageCardWidgetState();
}

class _MessageCardWidgetState extends State<MessageCardWidget> {
  final ScrollController contentScrollController = ScrollController();
  bool showArrow = false;

  @override
  void initState() {
    super.initState();

    // Check if content is scrollable after first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (contentScrollController.position.maxScrollExtent > 0) {
        setState(() {
          showArrow = true;
        });
      }
    });

    // Listen to scroll changes to update the arrow visibility
    contentScrollController.addListener(() {
      if (contentScrollController.position.atEdge) {
        if (contentScrollController.position.pixels ==
            contentScrollController.position.maxScrollExtent) {
          // Scrolled to the bottom
          if (showArrow) {
            setState(() {
              showArrow = false;
            });
          }
        } else {
          // At the top
          if (!showArrow) {
            setState(() {
              showArrow = true;
            });
          }
        }
      } else {
        if (!showArrow) {
          setState(() {
            showArrow = true;
          });
        }
      }
    });
  }

  @override
  void dispose() {
    contentScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(5),
      margin: const EdgeInsets.fromLTRB(0, 0, 10, 5),
      width: widget.itemWidth,
      decoration: BoxDecoration(
        color: MyColors.myDarkGray,
        borderRadius: BorderRadius.circular(15),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Message header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  children: [
                    Icon(Icons.message, color: Colors.white, size: 16),
                    const SizedBox(width: 5),
                    Expanded(
                      child: Text(
                        widget.message.title ?? 'UnknownSender'.tr(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    widget.message.sendingDate ?? '',
                    style: const TextStyle(
                      color: Colors.grey,
                      fontSize: 10,
                    ),
                  ),
                  Text(
                    widget.message.sendingTime ?? '',
                    style: const TextStyle(
                      color: Colors.grey,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const Divider(),
          Expanded(
            child: Stack(
              children: [
                SingleChildScrollView(
                  controller: contentScrollController,
                  child: Text(
                    widget.message.content ?? 'NoMessage'.tr(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                    textAlign: TextAlign.start,
                  ),
                ),
                // Red arrow indication at the bottom left when scrollable
                if (showArrow)
                  const Positioned(
                    bottom: 10,
                    left: 10,
                    child: Icon(
                      Icons.arrow_downward,
                      size: 16,
                      color:
                          ColorManager.myOrangeColor, // Red color for the arrow
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
