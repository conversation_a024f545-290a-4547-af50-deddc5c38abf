// close_activity_widgets.dart
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gym_app/core/constants/color_manager.dart';
import 'package:gym_app/core/constants/my_colors.dart';

Widget buildCloseActivityHeader(BuildContext context) {
  return Container(
    margin: const EdgeInsets.symmetric(horizontal: 7),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Container(
          margin: const EdgeInsets.fromLTRB(0, 0, 10, 0),
          child: Text(
            'UpcomingActivity'.tr(),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
        GestureDetector(
          onTap: () {
            Navigator.pushReplacementNamed(context, '/booking');
          },
          child: Text(
            "ShowAll".tr(),
            style: TextStyle(color: ColorManager.myOrangeColor),
          ),
        ),
      ],
    ),
  );
}

Widget buildCloseActivityListView(
    BuildContext context, List closeActivities, ScrollController closeActivityScrollController) {
  if (closeActivities.isEmpty) {
    return SizedBox(
      height: 145,
      child: Center(
        child: Text(
          'NotYetCloseMeetingDetermined'.tr(),
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  return Container(
    margin: const EdgeInsets.fromLTRB(0, 0, 20, 0),
    height: 220, // Increased height to fit the image and text
    child: Scrollbar(
      thumbVisibility: true, // This makes the scrollbar always visible
      controller: closeActivityScrollController,
      child: ListView.builder(
        controller: closeActivityScrollController,
        itemCount: closeActivities.length,
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(vertical: 8),
        itemBuilder: (context, i) {
          double itemWidth = closeActivities.length == 1
              ? MediaQuery.of(context).size.width - 40 // Full width with padding
              : 180;

          int numberOfParticipants = closeActivities[i]['numberOfParticipants'] ?? 0;
          int maxMembers = closeActivities[i]['maxMembers'] ?? 0;
          String closeDate = closeActivities[i]['closeDate'] ?? '';
          int courseImageID = closeActivities[i]['CourseImageID'] ?? 0;
          bool isStudentJoined = closeActivities[i]['isStudentJoined'] ?? false;

          String durationText = '';
          String timeText = '';
          String dateText = '';
          String statusText = "Available".tr();
          Color statusBgColor = Colors.green;

          bool hasValidCloseDate = closeDate.isNotEmpty && closeDate.split(' ').length == 4;

          if (hasValidCloseDate) {
            List<String> dateTimeParts = closeDate.split(' '); // Splitting by space
            String startTime = dateTimeParts[1];
            String endTime = dateTimeParts[3];

            timeText = startTime;
            dateText = dateTimeParts[0];

            try {
              DateTime startDateTime = DateFormat('HH:mm').parse(startTime);
              DateTime endDateTime = DateFormat('HH:mm').parse(endTime);
              Duration duration = endDateTime.difference(startDateTime);

              if (duration.inMinutes > 0) {
                durationText = '${duration.inMinutes} ${"Minutes".tr()}';
              }
            } catch (e) {
              print('Error parsing time: $e');
              durationText = '';
            }

            if (maxMembers > 0 && numberOfParticipants >= maxMembers) {
              statusText = "Full".tr();
              statusBgColor = Colors.grey;
            }

            if (isStudentJoined == true) {
              statusText = "Joined".tr();
              statusBgColor = Colors.orange;
            }
          } else {
            statusText = "NotAvailable".tr();
            statusBgColor = Colors.red;
          }

          return Container(
            padding: const EdgeInsets.all(10),
            margin: const EdgeInsets.fromLTRB(10, 0, 10, 0),
            width: itemWidth,
            decoration: BoxDecoration(
              color: MyColors.myDarkGray,
              borderRadius: BorderRadius.circular(15),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Image Section
                ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: Image.asset(
                    'assets/images/Courses/${courseImageID}.jpg',
                    height: 80,
                    width: double.infinity,
                    fit: BoxFit.cover,
                  ),
                ),
                const SizedBox(height: 10),
                // Status and Date
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 2, horizontal: 10),
                      decoration: BoxDecoration(
                        color: statusBgColor,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        statusText,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const Spacer(),
                    if (hasValidCloseDate)
                      Text(
                        dateText,
                        style: const TextStyle(fontSize: 12, color: Colors.white),
                      ),
                  ],
                ),
                const SizedBox(height: 5),
                // Activity Name
                Flexible(
                  child: Text(
                    closeActivities[i]['name'] ?? 'N/A',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(height: 5),
                if (hasValidCloseDate) ...[
                  // Time and Duration
                  Row(
                    children: [
                      const Icon(Icons.access_time, color: Colors.red, size: 14),
                      const SizedBox(width: 5),
                      Flexible(
                        child: Text(
                          timeText,
                          style: const TextStyle(color: Colors.red, fontSize: 12),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 25),
                      const Icon(Icons.timer, color: Colors.red, size: 14),
                      const SizedBox(width: 5),
                      Flexible(
                        child: Text(
                          durationText,
                          style: const TextStyle(color: Colors.red, fontSize: 12),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ] else ...[
                  const SizedBox(height: 5),
                  Center(
                    child: Text(
                      'NoCloseMeeting'.tr(),
                      style: const TextStyle(
                        color: Colors.grey,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
                const SizedBox(height: 5),
                // Number of Participants
                if (hasValidCloseDate)
                  Row(
                    children: [
                      const Icon(Icons.people, color: Colors.orange, size: 14),
                      const SizedBox(width: 5),
                      Text(
                        numberOfParticipants.toString() +
                            "/" +
                            maxMembers.toString() +
                            " " +
                            "Place".tr(),
                        style: const TextStyle(
                            color: Colors.orange, fontSize: 12),
                      ),
                    ],
                  ),
              ],
            ),
          );
        },
      ),
    ),
  );
}
