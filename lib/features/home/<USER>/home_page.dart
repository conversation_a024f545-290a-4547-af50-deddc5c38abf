import 'dart:async';
import 'dart:ui';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gym_app/core/constants/color_manager.dart';
import 'package:gym_app/core/constants/my_colors.dart';
import 'package:gym_app/core/constants/strings.dart';
import 'package:gym_app/core/helper/cache_helper.dart';
import 'package:gym_app/core/helper/dio_helper.dart';
import 'package:gym_app/core/widgets/custom_bottom_navigationbar.dart';
import 'package:gym_app/core/widgets/custom_error_dialog.dart';
import 'package:gym_app/core/widgets/custom_popup_menu_button.dart';
import 'package:gym_app/core/widgets/lang_change_button.dart';
import 'package:gym_app/core/widgets/qr_scan_fab.dart';
import 'package:gym_app/features/home/<USER>/Widgets/activity_widgets.dart';
import 'package:gym_app/features/home/<USER>/Widgets/close_activity_widgets.dart';
import 'package:gym_app/features/home/<USER>/Widgets/last_messages_widget.dart';
import 'package:provider/provider.dart';

final canShowWidgets = DateTime.now().isAfter(DateTime.parse('2024-11-19'));

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _selectedIndex = 0;
  GlobalKey qrKey = GlobalKey();
  String? scanState;
  final PageController _subscriptionPageController = PageController();
  final PageController _activityPageController = PageController();
  final ScrollController _activityScrollController = ScrollController();
  final ScrollController _closeActivityScrollController =
      ScrollController(); // New controller
  int _currentSubscriptionPage = 0;
  int _currentActivityPage = 0;

  List subscriptions = []; // Empty list initially
  List activities = [];
  List closeActivities = [];
  bool _isLoadingSubscriptions = true; // Add loading state for subscriptions
  bool _isLoadingActivities = true; // Add loading state for activities
  bool hasSubscriptions = true;
  bool hasValidSubscription = false;

  @override
  void initState() {
    super.initState();
    _loadScanState();
    _fetchData();
    _fetchActivity();
    _fetchCloseActivity();
  }

  Future<void> _fetchData() async {
    setState(() {
      _isLoadingSubscriptions = true; // Set loading state to true
    });

    try {
      final response = await DioHelper.getData(
        url: '/api/Subscriptions/GetStudentSubscriptions',
      );

      var message = response.data['message'];
      if (response.statusCode == 200 && message == 'Succeeded') {
        final data = response.data['result'] as List;
        print(data);
        setState(() {
          subscriptions = data
              .map((item) => {
                    "name": item['name'],
                    "toDateTime": item['toDateTime'],
                    "fromDateTime": item['fromDateTime'],
                    "meetingsNumber": item['meetingsNumber'],
                    "cost": item['cost'],
                    "subscriptionStatusID": item['subscriptionStatus']
                        ['subscriptionStatusID'],
                    "usedMeetingsNumber": item['usedMeetingsNumber'],
                    // "HasEntrySubscription": item['hasEntrySubscription']
                    "isActive":item['isActive'],
                    "entriesNumber":item['entriesNumber'],
                    "usedEntriesNumber":item['usedEntriesNumber'],
                  })
              .toList();

          _isLoadingSubscriptions = false;
          // hasSubscriptions = subscriptions[0]['HasEntrySubscription'];
          hasSubscriptions = true;
          if (hasSubscriptions) {
            CacheHelper.setData(key: 'hasSubscriptions', value: true);
          } else {
            CacheHelper.setData(key: 'hasSubscriptions', value: false);
          }
        });
      } else {
        _isLoadingSubscriptions = false;
        _isLoadingActivities = false;
        setState(() {
          this.hasSubscriptions = false;
          // change local storage
        });
      }
    } catch (e) {
      print(e);
      CustomErrorDialog.showErrorDialog(context, 'CheckConnection'.tr());

      setState(() {
        _isLoadingSubscriptions = false; // Set loading state to false
      });
    }
  }

  Future<void> _fetchActivity() async {
    setState(() {
      _isLoadingActivities = true; // Set loading state to true
    });

    try {
      final response = await DioHelper.getData(
        url: '/api/Students/GetStudentActivities',
      );
      var message = response.data['message'];
      if (response.statusCode == 200 &&
          message == 'Succeeded' &&
          response.data['result'].length > 0) {
        final data = response.data['result'] as List;
        print(data.length);
        setState(() {
          activities = data
              .map((item) => {
                    "studentActivityTypeNumber":
                        item['studentActivityTypeNumber'],
                    "activityName1": item['activityName1'],
                    "activityName2": item['activityName2'],
                    "activityDate": item['activityDate'],
                    "fromTime": item['fromTime'],
                    "toTime": item['toTime']
                  })
              .toList();
          _isLoadingActivities = false; // Set loading state to false
        });
      } else {
        _isLoadingActivities = false;
      }
    } catch (e) {
      // _showErrorDialog('Error fetching activities: $e');
      // CustomErrorDialog.showErrorDialog(
      //     context, 'Error fetching subscriptions: $e');
      setState(() {
        _isLoadingActivities = false; // Set loading state to false
      });
    }
  }

  Future<void> _fetchCloseActivity() async {
    try {
      final response = await DioHelper.getData(
        url: '/api/Home/GetCloseMeetingStudent',
      );

      var message = response.data['message'];
      if (response.statusCode == 200 && message == 'Get Succeeded') {
        final data = response.data['result'] as List;
        print(data);
        setState(() {
          closeActivities = data.map((item) {
            return {
              "name": item['name'] ?? 'N/A',
              "closeDate": item['closeDate'] ?? 'N/A',
              "price": item['price'] ?? 0,
              "priceType": item['priceType'] ?? 0,
              "numberOfParticipants": item['numberOfParticipants'] ?? 0,
              "maxMembers": item['maxMembers'] ?? 0,
              "CourseImageID": item['courseImageID'] ?? 0,
              "isStudentJoined": item['isStudentJoined'] ?? false,
            };
          }).toList();

          _isLoadingSubscriptions =
              false; // Consider a separate state for closeActivities
        });
      } else {
        Locale? currentLocal = EasyLocalization.of(context)!.currentLocale;
        var failedMessage = '';
        if (currentLocal == const Locale('he', 'DZ')) {
          failedMessage = response.data['result']['failedMessage1'];
        } else {
          failedMessage = response.data['result']['failedMessage2'];
        }
        CustomErrorDialog.showErrorDialog(context, failedMessage);
      }
    } catch (e) {
      print(e);
      // CustomErrorDialog.showErrorDialog(
      //     context, 'Error fetching close activities: $e');

      setState(() {
        _isLoadingSubscriptions = false;
      });
    }
  }

  void _loadScanState() async {
    scanState = CacheHelper.getData(key: 'scanState') as String?;
    scanState ??= 'exit';
    setState(() {});
  }

  void _toggleScanState() async {
    scanState = (scanState == 'entered') ? 'exit' : 'entered';
    await CacheHelper.setData(key: 'scanState', value: scanState);
    setState(() {});
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });

    switch (index) {
      case 0:
        break;
      case 1:
        Navigator.pushReplacementNamed(context, '/booking');
        break;
    }
  }

  @override
  void dispose() {
    _subscriptionPageController.dispose();
    _activityPageController.dispose();
    _activityScrollController.dispose();
    _closeActivityScrollController.dispose();
    super.dispose();
  }

  void _nextSubscriptionPage() {
    if (_currentSubscriptionPage < subscriptions.length - 1) {
      _currentSubscriptionPage++;
      _subscriptionPageController.animateToPage(
        _currentSubscriptionPage,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousSubscriptionPage() {
    if (_currentSubscriptionPage > 0) {
      _currentSubscriptionPage--;
      _subscriptionPageController.animateToPage(
        _currentSubscriptionPage,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        // return AlertDialog(
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
          child: AlertDialog(
            insetPadding: const EdgeInsets.symmetric(horizontal: 30),
            backgroundColor: ColorManager.myDialogBackgroundColor,
            title: Text('ErrorOccurred'.tr(),
                style: const TextStyle(
                    color: Colors.red,
                    fontSize: 26,
                    fontWeight: FontWeight.w600)),
            content: Text(
              message,
              style: const TextStyle(
                  color: Colors.white,
                  fontSize: 22,
                  fontWeight: FontWeight.w600),
            ),
            actions: <Widget>[
              TextButton(
                child: Text(
                  'Close'.tr(),
                  style: const TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.w600),
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  bool isArabic(String text) {
    final arabicRegex = RegExp(r'[\u0600-\u06FF]');
    return arabicRegex.hasMatch(text);
  }

  String _convertArabicToEnglish(String input) {
    return input
        .replaceAll('٠', '0')
        .replaceAll('١', '1')
        .replaceAll('٢', '2')
        .replaceAll('٣', '3')
        .replaceAll('٤', '4')
        .replaceAll('٥', '5')
        .replaceAll('٦', '6')
        .replaceAll('٧', '7')
        .replaceAll('٨', '8')
        .replaceAll('٩', '9');
  }

  @override
  Widget build(BuildContext context) {
    bool isConnectedToInternet = Provider.of<bool>(context);
    Color buttonColor =
        (scanState == 'entered') ? Colors.green : ColorManager.myOrangeColor;
    Color iconColor = (scanState == 'entered') ? Colors.black : Colors.white;

    return Scaffold(
      backgroundColor: ColorManager.myBackgroundColor,
      appBar: AppBar(
        iconTheme: const IconThemeData(
          color: Colors.white,
        ),
        backgroundColor: ColorManager.myBackgroundColor,
        title: Text(
          CacheHelper.getData(key: 'organization'),
          style: const TextStyle(
              fontSize: 18,
              color: ColorManager.myOrangeColor,
              fontWeight: FontWeight.bold),
        ),
        actions: <Widget>[
          Row(
            children: [
              if (canShowWidgets)
                Container(
                  child: Tooltip(
                    message: "Messages".tr(),
                    child: GestureDetector(
                      onTap: () {
                        Navigator.pushNamed(context, '/messages');
                      },
                      child: Container(
                        padding: const EdgeInsets.all(5),
                        decoration: BoxDecoration(
                          color: const Color(0xff323232),
                          borderRadius: BorderRadius.circular(50),
                        ),
                        child: SvgPicture.string(
                          height: 26,
                          Strings.messageIcon,
                          color: ColorManager.myOrangeColor,
                        ),
                      ),
                    ),
                  ),
                ),
              const SizedBox(width: 15),
              const LanguageChangeButton(),
              const SizedBox(width: 10),
              Container(
                  margin: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    color: const Color(0xff323232),
                  ),
                  child: CustomPopupMenuButton(parentContext: context))
            ],
          ),
        ],
      ),
      body: isConnectedToInternet
          ? _isLoadingSubscriptions || _isLoadingActivities
              ? Center(
                  child: SizedBox(
                    height: 200.0,
                    width: 200.0,
                    child: Container(
                      child: Image.asset(
                        'assets/images/14_screen_loading.gif',
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                )
              : SingleChildScrollView(
                  child: Container(
                    color: ColorManager.myBackgroundColor,
                    child: Column(
                      children: <Widget>[
                        const SizedBox(height: 20),
                        _buildHeaderRow(context),
                        const SizedBox(height: 25),
                        _buildSubscriptionHeader(),
                        const SizedBox(height: 15),
                        _buildSubscriptionPageView(),
                        const SizedBox(height: 15),
                        if (canShowWidgets) LastMessagesWidget(),
                        const SizedBox(height: 15),
                        buildActivityHeader(context),
                        const SizedBox(height: 15),
                        buildActivityListView(
                            context, activities, _activityScrollController),
                        const SizedBox(height: 30),
                        buildCloseActivityHeader(context),
                        const SizedBox(height: 15),
                        buildCloseActivityListView(context, closeActivities,
                            _closeActivityScrollController),
                        const SizedBox(height: 40),
                      ],
                    ),
                  ),
                )
          : Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.wifi_off, size: 100, color: Colors.red),
                  const SizedBox(height: 20),
                  Text(
                    'ConnectToInternet'.tr(),
                    style: const TextStyle(fontSize: 18, color: Colors.red),
                  ),
                ],
              ),
            ),
      bottomNavigationBar: CustomBottomNavigationBar(
        selectedIndex: _selectedIndex,
        onItemTapped: _onItemTapped,
      ),
      floatingActionButton: QRScanFAB(
        onScanSuccess: (qrCodeString) {
          print('QR Code Scanned in HomePage: $qrCodeString');
          _fetchActivity();
          _toggleScanState(); // Update the state after successful scan
        },
        qrKey: qrKey,
        buttonColor: isConnectedToInternet && hasSubscriptions
            ? buttonColor
            : Colors.grey,
        iconColor:
            isConnectedToInternet && hasSubscriptions ? iconColor : Colors.grey,
        isEnabled: (isConnectedToInternet && hasSubscriptions),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }

  Widget _buildHeaderRow(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(0, 0, 15, 0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '👋 ' + '${'Hello'.tr()} ',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          isArabic(CacheHelper.getData(key: 'fullName'))
              ? Text(
                  CacheHelper.getData(key: 'fullName'),
                  style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontFamily: 'Cairo'),
                )
              : Text(
                  CacheHelper.getData(key: 'fullName'),
                  style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontFamily: 'Heebo'),
                ),
        ],
      ),
    );
  }

  Widget _buildSubscriptionHeader() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 7),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            margin: const EdgeInsets.fromLTRB(0, 0, 10, 0),
            child: Text(
              'MySubscription'.tr(),
              style: const TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.w400),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubscriptionPageView() {
    if (subscriptions.isEmpty) {
      return SizedBox(
        height: 145,
        child: Center(
          child: Text(
            'NoClubSubscriptionFound'.tr(),
            style: const TextStyle(
              color: Colors.grey,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      );
    }
    return Stack(
      alignment: Alignment.center,
      children: [
        SizedBox(
          height: 150,
          child: PageView.builder(
            controller: _subscriptionPageController,
            onPageChanged: (index) {
              setState(() {
                _currentSubscriptionPage = index;
              });
            },
            itemCount: subscriptions.length,
            itemBuilder: (context, i) {
              IconData statusIcon = Icons.error;
              Color statusIconColor = Colors.green;
              Color backgroundIconColor = Colors.black;

              // Safely parsing Dates with Arabic numeral conversion
              String fromDateTimeStr = subscriptions[i]['fromDateTime'] ?? '01/01/1970';
              String toDateTimeStr = subscriptions[i]['toDateTime'] ?? '01/01/1970';

              // Convert Arabic numerals to English before parsing
              fromDateTimeStr = _convertArabicToEnglish(fromDateTimeStr);
              toDateTimeStr = _convertArabicToEnglish(toDateTimeStr);

              // Parse dates with explicit locale to avoid format issues
              DateTime fromDateTime = DateFormat('dd/MM/yyyy', 'en_US').parse(fromDateTimeStr);
              DateTime toDateTime = DateFormat('dd/MM/yyyy', 'en_US').parse(toDateTimeStr);
              DateTime now = DateTime.now();

              // Calculate total duration and elapsed duration
              int totalDuration = toDateTime.difference(fromDateTime).inDays + 1;
              int elapsedDuration = now.difference(fromDateTime).inDays + 1;
              double percentage = (elapsedDuration / totalDuration) * 100;

              if (subscriptions[i]['meetingsNumber'] ==
                      subscriptions[i]['usedMeetingsNumber'] &&
                  subscriptions[i]['meetingsNumber'] != 0) {
                percentage = 100;
              }

              // Limit percentage to a maximum of 100%
              double limitedPercentage =
                  percentage.isNaN ? 0.0 : percentage.clamp(0, 100);
              // Change color based on progress
              Color progressColor =
                  limitedPercentage == 100 ? Colors.red : statusIconColor;

              // Calculate the total months
              int totalMonths = ((toDateTime.year - fromDateTime.year) * 12) +
                  (toDateTime.month - fromDateTime.month);
              int elapsedMonths = ((now.year - fromDateTime.year) * 12) +
                  (now.month - fromDateTime.month);

              
              if(subscriptions[i]['subscriptionStatusID']==3 && subscriptions[i]['isActive'] == false){
                subscriptions[i]['subscriptionStatusID'] = 1;
              }
              switch (subscriptions[i]['subscriptionStatusID']) {
                case 3:
                  statusIcon = Icons.check;
                  statusIconColor = Colors.green;
                  backgroundIconColor = Colors.black;
                  break;
                case 1:
                  statusIcon = Icons.error_outline_sharp;
                  statusIconColor = Colors.red;
                  backgroundIconColor = Colors.black;
                  break;
                case 2:
                  statusIcon = Icons.pause;
                  statusIconColor = Colors.orange;
                  backgroundIconColor = Colors.black;
                  break;
                default:
                  statusIcon = Icons.error;
                  statusIconColor = Colors.red;
                  backgroundIconColor = Colors.black;
              }

              return Container(
                width: MediaQuery.of(context).size.width,
                margin: const EdgeInsets.symmetric(horizontal: 30),
                decoration: BoxDecoration(
                  color: MyColors.myDarkGray,
                  borderRadius: BorderRadius.circular(15),
                ),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            // padding: const EdgeInsets.all(8),
                            padding: const EdgeInsets.all(3),
                            child: Text(
                              subscriptions[i]['name'] ?? 'N/A',
                              style: const TextStyle(
                                fontSize: 15,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const Spacer(),
                          Align(
                            alignment: Alignment.centerRight,
                            child: Container(
                              padding: const EdgeInsets.all(3),
                              margin: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                              decoration: BoxDecoration(
                                color: backgroundIconColor,
                                borderRadius: BorderRadius.circular(100),
                              ),
                              child: Icon(
                                statusIcon,
                                color: statusIconColor,
                                // size: 30,
                                size: 20,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const Divider(color: Colors.white),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            child: RichText(
                              text: TextSpan(
                                style: const TextStyle(
                                    color: Colors.white), // Default text style
                                children: [
                                  TextSpan(
                                    text: "${"Validity".tr()} : ",
                                    style: const TextStyle(
                                        fontSize:
                                            12), // Font size 12 for "Validity"
                                  ),
                                  TextSpan(
                                    text: subscriptions[i]['toDateTime'] != null
                                        ? '${subscriptions[i]['toDateTime']} - ${subscriptions[i]['fromDateTime']}'
                                        : ' ${"WithoutValidity".tr()}',
                                    style: TextStyle(
                                      fontSize: subscriptions[i]
                                                  ['toDateTime'] !=
                                              null
                                          ? 11
                                          : 12, // Font size 9 for dates, 12 for WithoutValidity
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            child: Text(
                              '${"Cost".tr()} : ${subscriptions[i]['cost']}',
                              // Cost information positioned on the left
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                      if (subscriptions[i]['usedMeetingsNumber'] != null)
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8),
                              child: Text(
                                subscriptions[i]['meetingsNumber'] >=
                                            subscriptions[i]
                                                ['usedMeetingsNumber'] &&
                                        subscriptions[i]['meetingsNumber'] > 0
                                    ? "${'NumberOfMeeting'.tr()}: ${subscriptions[i]['usedMeetingsNumber'] ?? 0}/${subscriptions[i]['meetingsNumber'] ?? 0}"
                                    : "${'NumberOfMeeting'.tr()}: ${subscriptions[i]['usedMeetingsNumber'] ?? 0}",
                                style: const TextStyle(
                                  color: Colors.white,
                                  // fontSize: 16
                                  fontSize: 12,
                                ),
                              ),
                            ),
                        if (subscriptions[i]['entriesNumber'] != null && subscriptions[i]['entriesNumber'] != 0)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: Text(
                            subscriptions[i]['entriesNumber'] >=
                                        subscriptions[i]
                                            ['usedEntriesNumber'] &&
                                    subscriptions[i]['entriesNumber'] > 0
                                ? "${'NumberOfEntries'.tr()}: ${subscriptions[i]['usedEntriesNumber'] ?? 0}/${subscriptions[i]['entriesNumber'] ?? 0}"
                                : "${'NumberOfEntries'.tr()}: ${subscriptions[i]['usedEntriesNumber'] ?? 0}",
                            style: const TextStyle(
                              color: Colors.white,
                              // fontSize: 16
                              fontSize: 12,
                            ),
                          ),
                        ),
                          ],
                        ),


                      const SizedBox(height: 8),
                      Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: Row(
                            children: [
                              const SizedBox(width: 8),
                              Expanded(
                                child: Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    // color: Colors.grey[800],
                                    color: const Color.fromARGB(
                                        255, 126, 125, 125),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text("SubscriptionPeriod".tr(),
                                          style: const TextStyle(
                                              color: Color(0xFFDBDBDB),
                                              fontSize: 12)),
                                      const SizedBox(height: 8),
                                      totalMonths != 0
                                          ? Text(
                                              "$totalMonths ${"Months".tr()}",
                                              style: const TextStyle(
                                                color: Colors.white,
                                                fontWeight: FontWeight.w800,
                                                fontSize: 12,
                                              ),
                                            )
                                          : (subscriptions[i]['toDateTime'] !=
                                                  null
                                              ? Text(
                                                  "LessThanAMonth".tr(),
                                                  style: const TextStyle(
                                                    color: Colors.white,
                                                    fontWeight: FontWeight.w800,
                                                    fontSize: 12,
                                                  ),
                                                )
                                              : Text(
                                                  ' ${"WithoutValidity".tr()}',
                                                  style: const TextStyle(
                                                    color: Colors.white,
                                                    fontWeight: FontWeight.w800,
                                                    fontSize: 12,
                                                  )))
                                    ],
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: const Color.fromARGB(
                                        255, 126, 125, 125),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Progress".tr(),
                                        style: const TextStyle(
                                          color: Color(0xFFDBDBDB),
                                          fontSize: 12,
                                        ),
                                      ),
                                      Text(
                                        "${limitedPercentage.toStringAsFixed(1)}%",
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.w800,
                                          fontSize: 10,
                                        ),
                                      ),
                                      const SizedBox(height: 5),
                                      LinearProgressIndicator(
                                        value: limitedPercentage / 100,
                                        backgroundColor: Colors.grey,
                                        color: progressColor,
                                        minHeight: 8,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          )),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
        if (_currentSubscriptionPage > 0)
          Positioned(
            right: 0,
            child: IconButton(
              icon: const Icon(Icons.arrow_back_ios),
              onPressed: _previousSubscriptionPage,
              color: Colors.white,
            ),
          ),
        if (_currentSubscriptionPage < subscriptions.length - 1)
          Positioned(
            left: -4,
            child: IconButton(
              icon: const Icon(Icons.arrow_forward_ios),
              onPressed: _nextSubscriptionPage,
              color: Colors.white,
            ),
          ),
      ],
    );
  }
}
