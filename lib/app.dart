import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gym_app/core/widgets/connection_checker.dart';
import 'package:gym_app/features/Messages/presentation/messages_page.dart';
import 'package:gym_app/features/articles/presentation/articles_page.dart';
import 'package:gym_app/features/authentication/presentation/auth_code_page.dart';
import 'package:gym_app/features/authentication/presentation/login_page.dart';
import 'package:gym_app/features/authentication/presentation/organization_page.dart';
import 'package:gym_app/features/booking/presentation/booking_page.dart';
import 'package:gym_app/features/favorites/presentation/posts_page.dart';
import 'package:gym_app/features/home/<USER>/home_page.dart';
import 'package:gym_app/features/onBoarding/presentation/onboarding.dart';
import 'package:gym_app/features/profile/presentation/profile_page.dart';
import 'package:provider/provider.dart';

class MyApp extends StatelessWidget {
  final bool isLoggedIn;
  static final navigatorKey = GlobalKey<NavigatorState>();

  const MyApp({super.key, required this.isLoggedIn});

  @override
  Widget build(BuildContext context) {
    Locale? currentLocale = EasyLocalization.of(context)!.currentLocale;
    String fontFamily;

    if (currentLocale == const Locale('he', 'DZ')) {
      fontFamily = 'Heebo'; // Replace with your default font family
      print(fontFamily);
    } else {
      fontFamily = 'Cairo'; // Replace with your Arabic font family
    }

    return StreamProvider<bool>(
      initialData: true,
      create: (context) => ConnectivityService().connectionStatus,
      child: MaterialApp(
        navigatorKey: MyApp.navigatorKey,
        debugShowCheckedModeBanner: false,
        localizationsDelegates: context.localizationDelegates,
        supportedLocales: context.supportedLocales,
        locale: context.locale,
        theme: ThemeData(
          // fontFamily: 'Heebo',
          fontFamily: fontFamily,
        ),
        title: 'Login App',
        onGenerateRoute: (settings) {
          switch (settings.name) {
            case '/':
              return MaterialPageRoute(
                  builder: (context) {
                    if (isLoggedIn) {
                      return const HomePage();
                    } else {
                      return const OnBoarding();
                    }
                  },
                  settings: const RouteSettings(name: '/'));
            case '/login':
              return MaterialPageRoute(
                  builder: (context) => const LoginPage(),
                  settings: const RouteSettings(name: '/login'));
            case '/organization':
              final args = settings.arguments as Map<String, dynamic>;
              return MaterialPageRoute(
                  builder: (context) => OrganizationPage(
                        organizations: args['organizations'],
                        token: args['token'],
                      ),
                  settings: const RouteSettings(name: '/organization'));
            case '/auth':
              final args = settings.arguments as Map<String, dynamic>;
              return MaterialPageRoute(
                  builder: (context) => AuthCodePage(
                        studentID: args['studentID'],
                        organizationID: args['organizationID'],
                        token: args['token'],
                      ),
                  settings: const RouteSettings(name: '/auth'));
            case '/home':
              return MaterialPageRoute(
                  builder: (context) => const HomePage(),
                  settings: const RouteSettings(name: '/home'));
            case '/articles':
              return MaterialPageRoute(
                  builder: (context) => const ArticlesPage(),
                  settings: const RouteSettings(name: '/articles'));
            case '/profile':
              return MaterialPageRoute(
                  builder: (context) => const ProfilePage(),
                  settings: const RouteSettings(name: '/profile'));
            case '/posts':
              return MaterialPageRoute(
                  builder: (context) => const PostsPage(),
                  settings: const RouteSettings(name: '/posts'));
            case '/booking':
              return MaterialPageRoute(
                  builder: (context) => const BookingPage(),
                  settings: const RouteSettings(name: '/booking'));
            case '/messages':
              return MaterialPageRoute(
                  builder: (context) => const MessagesPage(),
                  settings: const RouteSettings(name: '/messages'));
            default:
              return null;
          }
        },
      ),
    );
  }
}
