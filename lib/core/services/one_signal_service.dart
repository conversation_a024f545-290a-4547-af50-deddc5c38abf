import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:gym_app/app.dart';
import 'package:gym_app/core/helper/cache_helper.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';

class OneSignalNotificationService {
  static Future<void> initOneSignal() async {
    OneSignal.Debug.setLogLevel(kDebugMode ? OSLogLevel.verbose : OSLogLevel.none,);
    OneSignal.initialize('************************************');
    final canRequestPermission = await OneSignal.Notifications.canRequest();
    if (canRequestPermission) {
      await OneSignal.Notifications.requestPermission(true);
    }
    oneSignalNotificationsClickListner();
  }

  static String getUserId() {
    final id = OneSignal.User.pushSubscription.id;
    return id ?? '';
  }

  static void oneSignalNotificationsClickListner() {
    OneSignal.Notifications.addClickListener((event) {
      bool isLoggedIn = CacheHelper.getData(key: 'isLoggedIn') ?? false;
      int selectedOrginizationID = CacheHelper.getData(key: 'orginizationID') ?? -1;
      int studentOrginizationIDReceivedTheMessage = event.notification.additionalData?.values.last;
      String studentOrginizationNameReceivedTheMessage = event.notification.additionalData?.values.first;
      if (isLoggedIn && studentOrginizationIDReceivedTheMessage == selectedOrginizationID) {
        popMessagesRouteIfPresent();
        MyApp.navigatorKey.currentState?.pushNamed('/messages');
      } else {
        ScaffoldMessenger.of(MyApp.navigatorKey.currentContext!).showSnackBar(
          SnackBar(
            content: Text(
              '${'PleaseLogInToYourAccountInOrganization'.tr()} $studentOrginizationNameReceivedTheMessage ${'ToSeeTheNewMessageReceived'.tr()}',
            ),
          ),
        );
      }
    });
  }

  static void popMessagesRouteIfPresent() {
    MyApp.navigatorKey.currentState?.popUntil((route) {
      if (route.settings.name == '/messages') {
        return false; 
      }
      return true;
    });
  }
  
}
