import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:gym_app/core/constants/strings.dart';
import 'cache_helper.dart';

class DioHelper {
  static late Dio dio;
  static String? accessToken;
  static bool isRefreshing = false;
  static const _storage = FlutterSecureStorage();

  static init() {
    //String ip = Strings.baseUrl;
    String baseUrl = Strings.baseUrl;
    dio = Dio(
      BaseOptions(
        //baseUrl: "http://$ip",
        baseUrl: baseUrl,
        receiveDataWhenStatusError: true,
      ),
    );

    dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        accessToken = CacheHelper.getData(key: 'token');
        if (accessToken != null) {
          options.headers['Authorization'] = 'Bearer $accessToken';
        }
        return handler.next(options);
      },
      onError: (DioException error, handler) async {
        if (error.response?.statusCode == 401 && !isRefreshing) {
          print("401 Unauthorized error received");
          if (CacheHelper.getData(key: 'refresh_token') != null) {
            isRefreshing = true;
            bool tokenRefreshed = await refreshToken();
            isRefreshing = false;
            if (tokenRefreshed) {
              print("Token refreshed successfully");
              return handler.resolve(await _retry(error.requestOptions));
            } else {
              print("Token refresh failed");
            }
          }
        }
        return handler.next(error);
      },
    ));
  }

  static Future<bool> isTokenExpired(String token) async {
    // Decode the token and check expiration
    try {
      final parts = token.split('.');
      if (parts.length != 3) {
        return true;
      }

      final payload = _decodeBase64(parts[1]);
      final payloadMap = json.decode(payload);

      if (payloadMap is! Map<String, dynamic>) {
        return true;
      }

      final exp = payloadMap['exp'];
      if (exp == null) {
        return true;
      }

      final expiryDate = DateTime.fromMillisecondsSinceEpoch(exp * 1000);
      return DateTime.now().isAfter(expiryDate);
    } catch (e) {
      print('Error checking token expiry: $e');
      return true;
    }
  }

  static String _decodeBase64(String str) {
    String output = str.replaceAll('-', '+').replaceAll('_', '/');
    switch (output.length % 4) {
      case 0:
        break;
      case 2:
        output += '==';
        break;
      case 3:
        output += '=';
        break;
      default:
        throw Exception('Illegal base64url string!"');
    }
    return utf8.decode(base64Url.decode(output));
  }

  static Future<Response> getData({
    required String url,
    Map<String, dynamic>? query,
  }) async {
    return await dio.get(
      url,
      queryParameters: query,
    );
  }

  static Future<Response> postData({
    required String url,
    required Map<String, dynamic> data,
    Map<String, dynamic>? headers,
  }) async {
    return await dio.post(
      url,
      data: data,
      options: Options(
        headers: headers,
      ),
    );
  }

  static Future<Response> putData({
    required String url,
    Map<String, dynamic>? query,
    String? token,
  }) async {
    return await dio.put(
      url,
      queryParameters: query,
      options: Options(
        headers: {
          'Authorization': 'Bearer $token',
        },
      ),
    );
  }

  static Future<Response> deleteData({
    required String url,
    Map<String, dynamic>? query,
    String? token,
  }) async {
    return await dio.delete(
      url,
      queryParameters: query,
      options: Options(
        headers: {
          'Authorization': 'Bearer $token',
        },
      ),
    );
  }

  static Future<bool> refreshToken() async {
    try {
      final refreshToken = CacheHelper.getData(key: 'refresh_token');
      final response = await dio.post(
          '/api/Authenticate/RefreshStudentAppAccessToken',
          data: {'RefreshToken': refreshToken});

      if (response.statusCode == 200) {
        final responseData = response.data['result'];
        accessToken = responseData['token'];
        String newRefreshToken = responseData['refreshToken'];
        await CacheHelper.setData(key: 'token', value: accessToken);
        await CacheHelper.setData(key: 'refresh_token', value: newRefreshToken);
        return true;
      } else {
        await _storage.deleteAll();
        return false;
      }
    } catch (e) {
      print("Error refreshing token: $e");
      return false;
    }
  }

  static Future<Response<dynamic>> _retry(RequestOptions requestOptions) async {
    final options = Options(
      method: requestOptions.method,
      headers: requestOptions.headers,
    );
    return dio.request<dynamic>(
      requestOptions.path,
      data: requestOptions.data,
      queryParameters: requestOptions.queryParameters,
      options: options,
    );
  }

  static Future<void> storeStudentPlayerIDInDB(String playerID) async {
    
    try {
      Response response = await DioHelper.postData(
        url: "/api/Authenticate/StoreStudentPlayerIDInDB",
        data: {
          "PlayerID": playerID,
        },
      );
      if (response.statusCode == 200) {
        if (response.data['message'] == 'Succeeded') {
          await CacheHelper.removeData(key: 'token');
        }
        return;
      } else {
        return;
      }
    } catch (e) {
      return;
    }
  }

  static Future<void> studentLogOut() async {
    try {
      Response response = await DioHelper.postData(
        url: "/api/Authenticate/StudentLogOut",
        data: {},
      );
      if (response.statusCode == 200) {
        return;
      } else {
        return;
      }
    } catch (e) {
      return;
    }
  }

}
