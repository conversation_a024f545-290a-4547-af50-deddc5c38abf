import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gym_app/core/constants/color_manager.dart';
import 'package:gym_app/core/constants/my_colors.dart';
import 'package:gym_app/core/helper/dio_helper.dart';
import 'package:gym_app/core/widgets/custom_error_dialog.dart';
import 'package:gym_app/features/booking/presentation/booking_page.dart';

void showBookingDialog(
    BuildContext context, Schedule schedule, VoidCallback onSuccess) {
  bool isButtonDisabled = false; // Track button state

  showDialog(
    context: context,
    builder: (BuildContext context) {
      return StatefulBuilder(
        builder: (BuildContext context, StateSetter setState) {
          return BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
            child: AlertDialog(
              insetPadding: const EdgeInsets.symmetric(horizontal: 30),
              backgroundColor: ColorManager.myDialogBackgroundColor,
              title: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(3),
                    margin: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                    decoration: BoxDecoration(
                        color: ColorManager.myGreenDialogButtonColor,
                        borderRadius: BorderRadius.circular(100)),
                    child: const Icon(Icons.check),
                  ),
                  Text(
                    'BookingConfirmation'.tr(),
                    style: const TextStyle(
                        color: Colors.white,
                        fontSize: 26,
                        fontWeight: FontWeight.w600),
                  ),
                ],
              ),
              content: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    RichText(
                      text: TextSpan(
                        style:
                            const TextStyle(color: Colors.white, fontSize: 16),
                        children: [
                          TextSpan(text: '${schedule.courseMeetingDate}\n'),
                          TextSpan(
                            text: '${'FromTime'.tr()}',
                            style: const TextStyle(color: MyColors.myTextPopup),
                          ),
                          TextSpan(
                              text:
                                  ' : ${schedule.fromTime} - ${schedule.toTime}\n'),
                          TextSpan(
                            text: '${'Activity'.tr()}',
                            style: const TextStyle(color: MyColors.myTextPopup),
                          ),
                          TextSpan(text: ' : ${schedule.courseName}\n'),
                          TextSpan(
                            text: '${'Description'.tr()}',
                            style: const TextStyle(color: MyColors.myTextPopup),
                          ),
                          TextSpan(text: ' : ${schedule.description}'),
                        ],
                      ),
                    ),
                    RichText(
                      text: TextSpan(
                        style:
                            const TextStyle(color: Colors.white, fontSize: 16),
                        children: [
                          TextSpan(
                            text: '${'NumberMembers'.tr()}',
                            style: const TextStyle(color: MyColors.myTextPopup),
                          ),
                          TextSpan(
                            text:
                                ' : ${schedule.courseMeetingParticipantsNumber}/${schedule.maxMembers}',
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),
                    Text(
                      'ConfirmStatement'.tr(),
                      style: const TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.w400),
                    ),
                    const SizedBox(height: 20),
                    if (schedule.price != 0 && schedule.price != null)
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: 'MeetingCost'.tr() +
                                  ' : ', // Meeting cost label
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            TextSpan(
                              text: (schedule.price! % 1 == 0
                                  ? schedule.price!.toInt().toString() + '₪'
                                  : schedule.price!.toString() + '₪'),
                              style: const TextStyle(
                                color: Colors.yellow, // Price in yellow
                                fontSize: 16,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      )
                  ],
                ),
              ),
              actions: [
                Container(
                  padding: const EdgeInsets.all(5),
                  margin: const EdgeInsets.fromLTRB(0, 0, 0, 30),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: Text(
                          'Back'.tr(),
                          style: const TextStyle(
                              color: Color(0xFF7E7E7E),
                              fontSize: 20,
                              fontWeight: FontWeight.w600),
                        ),
                      ),
                      TextButton(
                        onPressed: isButtonDisabled
                            ? null
                            : () async {
                                if (schedule.maxMembers ==
                                    schedule.courseMeetingParticipantsNumber) {
                                  // Show custom error dialog when meeting is full
                                  CustomErrorDialog.showErrorDialog(
                                      context, 'MeetingIsFull'.tr(),
                                      header: 'Sorry'.tr());
                                } else {
                                  // Proceed with API request if not full
                                  setState(() {
                                    isButtonDisabled = true; // Disable button
                                  });
                                  try {
                                    final response = await DioHelper.postData(
                                      url:
                                          '/api/CoursesMeetings/InsertCourseMeetingToStudent',
                                      data: {
                                        'CourseMeetingID':
                                            int.parse(schedule.courseMeetingID)
                                      },
                                    );

                                    if (response.statusCode == 200 &&
                                        response.data['message'] ==
                                            'Succeeded') {
                                      Navigator.of(context)
                                          .pop(); // Close dialog
                                      onSuccess(); // Trigger success callback

                                      // Show success dialog
                                      showDialog(
                                        context: context,
                                        builder: (BuildContext context) {
                                          return BackdropFilter(
                                            filter: ImageFilter.blur(
                                                sigmaX: 5, sigmaY: 5),
                                            child: AlertDialog(
                                              insetPadding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 30),
                                              backgroundColor: ColorManager
                                                  .myDialogBackgroundColor,
                                              content: SizedBox(
                                                height: 220,
                                                child: Column(
                                                  children: [
                                                    Image.asset(
                                                      "assets/images/11_fireSuccessBooking.png",
                                                      fit: BoxFit.contain,
                                                      height: 120,
                                                    ),
                                                    const SizedBox(height: 30),
                                                    Flexible(
                                                      child: Text(
                                                        'ConfirmedStatementSuccess'
                                                            .tr(),
                                                        style: const TextStyle(
                                                            color: Colors.white,
                                                            fontSize: 20,
                                                            fontWeight:
                                                                FontWeight
                                                                    .w400),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              actions: [
                                                Container(
                                                  padding:
                                                      const EdgeInsets.all(5),
                                                  margin:
                                                      const EdgeInsets.fromLTRB(
                                                          0, 0, 0, 30),
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    children: [
                                                      TextButton(
                                                        onPressed: () {
                                                          Navigator.of(context)
                                                              .pop();
                                                        },
                                                        child: Text(
                                                          'Approve'.tr(),
                                                          style: const TextStyle(
                                                              color: Color(
                                                                  0xFF7E7E7E),
                                                              fontSize: 20,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w600),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        },
                                      );
                                    } else {
                                      Navigator.of(context).pop();
                                      var message = "";
                                      Locale? currentLocal =
                                          EasyLocalization.of(context)!
                                              .currentLocale;
                                      if (currentLocal ==
                                          const Locale('he', 'DZ')) {
                                        message = response.data['result']
                                            ['failedMessage1'];
                                      } else {
                                        message = response.data['result']
                                            ['failedMessage2'];
                                      }

                                      CustomErrorDialog.showErrorDialog(
                                        context,
                                        message,
                                      );
                                    }
                                  } catch (e) {
                                    CustomErrorDialog.showErrorDialog(
                                      context,
                                      'AskManager'.tr(),
                                    );
                                  } finally {
                                    setState(() {
                                      isButtonDisabled =
                                          false; // Re-enable button
                                    });
                                  }
                                }
                              },
                        child: Text(
                          'Confirmation'.tr(),
                          style: TextStyle(
                            color: isButtonDisabled
                                ? Colors.grey
                                : ColorManager.myGreenDialogButtonColor,
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      );
    },
  );
}

void showCancellationDialog(
    BuildContext context, Schedule schedule, VoidCallback onSuccess) {
  bool isButtonDisabled = false; // Track button state

  showDialog(
    context: context,
    builder: (BuildContext context) {
      return StatefulBuilder(
        builder: (BuildContext context, StateSetter setState) {
          return BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
            child: AlertDialog(
              insetPadding: const EdgeInsets.symmetric(horizontal: 30),
              backgroundColor: ColorManager.myDialogBackgroundColor,
              title: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(3),
                    margin: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                    decoration: BoxDecoration(
                        color: ColorManager.myRedDialogButtonColor,
                        borderRadius: BorderRadius.circular(100)),
                    child: const Icon(Icons.close),
                  ),
                  Text(
                    'CancellationConfirmation'.tr(),
                    style: const TextStyle(
                        color: Colors.white,
                        fontSize: 26,
                        fontWeight: FontWeight.w600),
                  ),
                ],
              ),
              content: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    RichText(
                      text: TextSpan(
                        style:
                            const TextStyle(color: Colors.white, fontSize: 16),
                        children: [
                          TextSpan(text: '${schedule.courseMeetingDate}\n'),
                          TextSpan(
                            text: '${'FromTime'.tr()}',
                            style: const TextStyle(color: MyColors.myTextPopup),
                          ),
                          TextSpan(
                              text:
                                  ' : ${schedule.fromTime} - ${schedule.toTime}\n'),
                          TextSpan(
                            text: '${'Activity'.tr()}',
                            style: const TextStyle(color: MyColors.myTextPopup),
                          ),
                          TextSpan(text: ' : ${schedule.courseName}\n'),
                          TextSpan(
                            text: '${'Description'.tr()}',
                            style: const TextStyle(color: MyColors.myTextPopup),
                          ),
                          TextSpan(text: ' : ${schedule.description}'),
                        ],
                      ),
                    ),
                    RichText(
                      text: TextSpan(
                        style:
                            const TextStyle(color: Colors.white, fontSize: 16),
                        children: [
                          TextSpan(
                            text: '${'NumberMembers'.tr()}',
                            style: const TextStyle(color: MyColors.myTextPopup),
                          ),
                          TextSpan(
                            text:
                                ' : ${schedule.courseMeetingParticipantsNumber}/${schedule.maxMembers}',
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 30),
                    Text(
                      'CancellationStatement'.tr(),
                      style: const TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.w400),
                    ),
                    SizedBox(height: 20),
                    if (schedule.price != 0 && schedule.price != null)
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: 'MeetingCost'.tr() +
                                  ' : ', // Meeting cost label
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            TextSpan(
                              text: (schedule.price! % 1 == 0
                                  ? schedule.price!.toInt().toString() + '₪'
                                  : schedule.price!.toString() + '₪'),
                              style: const TextStyle(
                                color: Colors.yellow, // Price in yellow
                                fontSize: 16,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      )
                  ],
                ),
              ),
              actions: [
                Container(
                  padding: const EdgeInsets.all(5),
                  margin: const EdgeInsets.fromLTRB(0, 0, 0, 30),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: Text(
                          'Back'.tr(),
                          style: const TextStyle(
                              color: Color(0xFF7E7E7E),
                              fontSize: 20,
                              fontWeight: FontWeight.w600),
                        ),
                      ),
                      TextButton(
                        onPressed: isButtonDisabled
                            ? null
                            : () async {
                                setState(() {
                                  isButtonDisabled = true; // Disable button
                                });
                                try {
                                  final response = await DioHelper.postData(
                                    url:
                                        '/api/CoursesMeetings/CancelCourseMeetingToStudent',
                                    data: {
                                      'CourseMeetingID':
                                          int.parse(schedule.courseMeetingID),
                                      'MeetingStudentID':
                                          schedule.meetingStudentID,
                                    },
                                  );

                                  if (response.statusCode == 200 &&
                                      response.data['message'] == 'Succeeded') {
                                    Navigator.of(context).pop();
                                    onSuccess();

                                    showDialog(
                                      context: context,
                                      builder: (BuildContext context) {
                                        return BackdropFilter(
                                          filter: ImageFilter.blur(
                                              sigmaX: 5, sigmaY: 5),
                                          child: AlertDialog(
                                            insetPadding:
                                                const EdgeInsets.symmetric(
                                                    horizontal: 30),
                                            backgroundColor: ColorManager
                                                .myDialogBackgroundColor,
                                            title: Row(
                                              children: [
                                                Container(
                                                  padding:
                                                      const EdgeInsets.all(3),
                                                  margin:
                                                      const EdgeInsets.fromLTRB(
                                                          10, 0, 0, 0),
                                                  decoration: BoxDecoration(
                                                      color: ColorManager
                                                          .myGreenDialogButtonColor,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              100)),
                                                  child:
                                                      const Icon(Icons.check),
                                                ),
                                                Text(
                                                  'CancellationConfirmation'
                                                      .tr(),
                                                  style: const TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 26,
                                                      fontWeight:
                                                          FontWeight.w600),
                                                ),
                                              ],
                                            ),
                                            content: SingleChildScrollView(
                                              child: Text(
                                                'CancelledStatementSuccess'
                                                    .tr(),
                                                style: const TextStyle(
                                                    color: Colors.white,
                                                    fontSize: 20,
                                                    fontWeight:
                                                        FontWeight.w400),
                                              ),
                                            ),
                                            actions: [
                                              Container(
                                                padding:
                                                    const EdgeInsets.all(5),
                                                margin:
                                                    const EdgeInsets.fromLTRB(
                                                        0, 0, 0, 30),
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    TextButton(
                                                      onPressed: () {
                                                        Navigator.of(context)
                                                            .pop();
                                                      },
                                                      child: Text(
                                                        'Approve'.tr(),
                                                        style: const TextStyle(
                                                            color: Color(
                                                                0xFF7E7E7E),
                                                            fontSize: 20,
                                                            fontWeight:
                                                                FontWeight
                                                                    .w600),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      },
                                    );
                                  } else {
                                    Navigator.of(context).pop();
                                    var message = "";
                                    Locale? currentLocal =
                                        EasyLocalization.of(context)!
                                            .currentLocale;
                                    if (currentLocal ==
                                        const Locale('he', 'DZ')) {
                                      message = response.data['result']
                                          ['failedMessage1'];
                                    } else {
                                      message = response.data['result']
                                          ['failedMessage2'];
                                    }

                                    showDialog(
                                      context: context,
                                      builder: (BuildContext context) {
                                        return BackdropFilter(
                                          filter: ImageFilter.blur(
                                              sigmaX: 5, sigmaY: 5),
                                          child: AlertDialog(
                                            insetPadding:
                                                const EdgeInsets.symmetric(
                                                    horizontal: 30),
                                            backgroundColor: ColorManager
                                                .myDialogBackgroundColor,
                                            content: SizedBox(
                                              height: 235,
                                              child: Column(
                                                children: [
                                                  Image.asset(
                                                    "assets/images/15_engin-akyurt-Lr7kDZmFq3U-unsplash (1).jpg",
                                                    fit: BoxFit.contain,
                                                    height: 120,
                                                  ),
                                                  const SizedBox(height: 30),
                                                  Flexible(
                                                    child: Text(
                                                      message,
                                                      style: const TextStyle(
                                                          color: Colors.white,
                                                          fontSize: 20,
                                                          fontWeight:
                                                              FontWeight.w400),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            actions: [
                                              Container(
                                                padding:
                                                    const EdgeInsets.all(5),
                                                margin:
                                                    const EdgeInsets.fromLTRB(
                                                        0, 0, 0, 30),
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    TextButton(
                                                      onPressed: () {
                                                        Navigator.of(context)
                                                            .pop();
                                                      },
                                                      child: Text(
                                                        'Approve'.tr(),
                                                        style: const TextStyle(
                                                            color: Color(
                                                                0xFF7E7E7E),
                                                            fontSize: 20,
                                                            fontWeight:
                                                                FontWeight
                                                                    .w600),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      },
                                    );
                                  }
                                } catch (e) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      // content: Text('Error: $e'),
                                      content: Text('AskManager'.tr()),
                                    ),
                                  );
                                } finally {
                                  setState(() {
                                    isButtonDisabled =
                                        false; // Re-enable button
                                  });
                                }
                              },
                        child: Text(
                          'Confirmation'.tr(),
                          style: const TextStyle(
                              color: ColorManager.myRedDialogButtonColor,
                              fontSize: 20,
                              fontWeight: FontWeight.w600),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      );
    },
  );
}

// if the meeting has passed
void showInfoDialog(BuildContext context, Schedule schedule) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: AlertDialog(
          insetPadding: const EdgeInsets.symmetric(horizontal: 30),
          backgroundColor: ColorManager.myDialogBackgroundColor,
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(3),
                margin: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                decoration: BoxDecoration(
                    color: ColorManager.myInfoDialogButtonColor,
                    borderRadius: BorderRadius.circular(100)),
                child: const Icon(Icons.info),
              ),
              Text(
                'MeetingInfo'.tr(),
                style: const TextStyle(
                    color: Colors.white,
                    fontSize: 26,
                    fontWeight: FontWeight.w600),
              ),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RichText(
                  text: TextSpan(
                    style: const TextStyle(color: Colors.white, fontSize: 16),
                    children: [
                      TextSpan(text: '${schedule.courseMeetingDate}\n'),
                      TextSpan(
                        text: '${'FromTime'.tr()}',
                        style: const TextStyle(color: MyColors.myTextPopup),
                      ),
                      TextSpan(
                          text:
                              ' : ${schedule.fromTime} - ${schedule.toTime}\n'),
                      TextSpan(
                        text: '${'Activity'.tr()}',
                        style: const TextStyle(color: MyColors.myTextPopup),
                      ),
                      TextSpan(text: ' : ${schedule.courseName}\n'),
                      TextSpan(
                        text: '${'Description'.tr()}',
                        style: const TextStyle(color: MyColors.myTextPopup),
                      ),
                      TextSpan(text: ' : ${schedule.description}'),
                    ],
                  ),
                ),
                RichText(
                  text: TextSpan(
                    style: const TextStyle(color: Colors.white, fontSize: 16),
                    children: [
                      TextSpan(
                        text: '${'NumberMembers'.tr()}',
                        style: const TextStyle(color: MyColors.myTextPopup),
                      ),
                      TextSpan(
                        text:
                            ' : ${schedule.courseMeetingParticipantsNumber}/${schedule.maxMembers}',
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 30),
                Text(
                  "MeetingHasPassed".tr(),
                  style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.w600),
                ),
                SizedBox(height: 20),
                if (schedule.price != 0 && schedule.price != null)
                  RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text:
                              'MeetingCost'.tr() + ' : ', // Meeting cost label
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        TextSpan(
                          text: (schedule.price! % 1 == 0
                              ? schedule.price!.toInt().toString() + '₪'
                              : schedule.price!.toString() + '₪'),
                          style: const TextStyle(
                            color: Colors.yellow, // Price in yellow
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                  )
              ],
            ),
          ),
          actions: [
            Container(
              padding: const EdgeInsets.all(5),
              margin: const EdgeInsets.fromLTRB(0, 0, 0, 30),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'Close'.tr(),
                      style: const TextStyle(
                          color: Color(0xFF7E7E7E),
                          fontSize: 20,
                          fontWeight: FontWeight.w600),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    },
  );
}
