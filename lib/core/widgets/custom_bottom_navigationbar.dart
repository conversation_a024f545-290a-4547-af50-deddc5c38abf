import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gym_app/core/constants/color_manager.dart';

class CustomBottomNavigationBar extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onItemTapped;

  const CustomBottomNavigationBar({
    super.key,
    required this.selectedIndex,
    required this.onItemTapped,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: Platform.isIOS ? null : 70,
      child: BottomNavigationBar(
        backgroundColor: Colors.black,
        unselectedItemColor: Colors.white,
        selectedItemColor: ColorManager.myOrangeColor,
        // selectedItemColor: Colors.orange,
        selectedFontSize: 14,
        unselectedFontSize: 14,
        currentIndex: selectedIndex,
        onTap: onItemTapped,
        type: BottomNavigationBarType.fixed,
        items: <BottomNavigationBarItem>[
          BottomNavigationBarItem(
            icon: const Icon(Icons.home, size: 22),
            label: 'Home'.tr(),
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.calendar_today, size: 22),
            label: 'Booking'.tr(),
          ),
          // BottomNavigationBarItem(
          //   icon: const Icon(Icons.article_outlined, size: 22),
          //   label: 'Articles'.tr(),
          // ),
          // BottomNavigationBarItem(
          //   icon: const Icon(Icons.person_3_outlined, size: 22),
          //   label: 'Profile'.tr(),
          // ),
        ],
      ),
    );
  }
}
