import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart'; 

class LocalizationCheacker {
  static changeLanguge(BuildContext context) {
    Locale? currentLocale = EasyLocalization.of(context)!.currentLocale;
    print('Current Locale Before Change: $currentLocale'); // Debugging log

    String currentLanguageCode = currentLocale!.languageCode;
    String currentCountryCode = currentLocale.countryCode ?? '';

    Locale newLocale;
    if (currentLanguageCode == 'he' && currentCountryCode == 'DZ') {
      newLocale = const Locale('ar', 'AE');
    } else {
      newLocale = const Locale('he', 'DZ');
    }

    // Change the locale with easy_localization
    EasyLocalization.of(context)!.setLocale(newLocale);

    // Also update the locale for GetX (GetMaterialApp)
    Get.updateLocale(newLocale);
  }
}



