// import 'package:flutter/material.dart';
// import 'package:easy_localization/easy_localization.dart';

// class ConfirmationDialog extends StatelessWidget {
//   final String title;
//   final String content;
//   final String confirmText;
//   final String cancelText;
//   final VoidCallback onConfirm;

//   const ConfirmationDialog({
//     Key? key,
//     required this.title,
//     required this.content,
//     required this.confirmText,
//     required this.cancelText,
//     required this.onConfirm,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return AlertDialog(
//       title: Text(title.tr()),
//       content: Text(content.tr()),
//       actions: <Widget>[
//         TextButton(
//           onPressed: () {
//             Navigator.of(context).pop(); // Close the dialog
//           },
//           child: Text(cancelText.tr()),
//         ),
//         TextButton(
//           onPressed: () {
//             Navigator.of(context).pop(); // Close the dialog
//             onConfirm(); // Trigger the confirm action
//           },
//           child: Text(confirmText.tr()),
//         ),
//       ],
//     );
//   }
// }

import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gym_app/core/constants/color_manager.dart';

class ConfirmationDialog extends StatelessWidget {
  final String title;
  final String content;
  final String confirmText;
  final String cancelText;
  final VoidCallback onConfirm;

  const ConfirmationDialog({
    Key? key,
    required this.title,
    required this.content,
    required this.confirmText,
    required this.cancelText,
    required this.onConfirm,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
      child: Dialog(
        insetPadding: const EdgeInsets.symmetric(horizontal: 30, vertical: 24),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15.0),
        ),
        backgroundColor: ColorManager.myDialogBackgroundColor,
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8, // Max height 80% of screen
          ),
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Text(
                    title.tr(),
                    style: const TextStyle(color: ColorManager.myOrangeColor,fontWeight: FontWeight.bold,fontSize: 19)
                  ),
                  const SizedBox(height: 20),
                  Text(
                    content.tr(),
                    style: const TextStyle(color: Colors.white),
                  ),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      TextButton(
                        style: TextButton.styleFrom(
                            foregroundColor: Colors.white,
                        ),
                        onPressed: () {
                          Navigator.of(context).pop(); // Close the dialog
                        },
                        child: Text(cancelText.tr()),
                      ),
                      Spacer(),
                      // const SizedBox(width: 8),
                      TextButton(
                        style: TextButton.styleFrom(
                            foregroundColor: Colors.red,
                        ),
                        onPressed: () {
                          Navigator.of(context).pop(); // Close the dialog
                          onConfirm(); // Trigger the confirm action
                        },
                        child: Text(confirmText.tr()),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
