import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gym_app/core/constants/color_manager.dart';
import 'package:gym_app/core/helper/cache_helper.dart';
import 'package:gym_app/core/helper/dio_helper.dart';
import 'package:gym_app/core/widgets/confirmation_dialog.dart';
import 'package:gym_app/features/onBoarding/presentation/onboarding.dart';

class CustomPopupMenuButton extends StatelessWidget {
  final BuildContext parentContext;

  const CustomPopupMenuButton({super.key, required this.parentContext});

  Future<void> _logout(BuildContext context) async {
    await DioHelper.studentLogOut();
    CacheHelper.removeAllData();
    Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute(builder: (context) => OnBoarding()),
      (route) => false,
    );
  }

  void _showLogoutConfirmation(BuildContext context) {
    int orginizationID = CacheHelper.getData(key: 'orginizationID');

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return ConfirmationDialog(
          title: 'LogoutConfirmation'.tr(),
          content: 'LogoutConfirmationStatement'.tr(),
          confirmText: 'Logout'.tr(),
          cancelText: 'Close'.tr(),
          onConfirm: () async {


            int entryID = CacheHelper.getData(key: 'studentEntryID') ?? 0;
            print("#####################");
            print(entryID);
            print("#####################");
            if (entryID == null) {
              CacheHelper.setData(key: 'studentEntryID', value: 0);
            }
            Response response;
            try {
              // --> exit
              if (entryID > 0) {
                response = await DioHelper.postData(
                  url: '/api/StudentsEntries/InsertStudentEntry',
                  data: {
                    'studentEntryID': entryID,
                    'Barcode': orginizationID,
                    'SubscriptionID': -1
                  },
                );
                if (response.statusCode == 200 &&
                    json.decode(response.toString())['message'] ==
                        "Succeeded") {
                  CacheHelper.setData(key: 'studentEntryID', value: 0);
                }
              }
            } catch (e) {
              log(e.toString());
            }
            _logout(parentContext);
            CacheHelper.setData(key: "isLoggedIn", value: false);
            CacheHelper.removeData(key: 'token');

          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints:
          BoxConstraints.tightFor(width: 35, height: 35), // Set fixed size
      child: PopupMenuButton(
        icon: Icon(Icons.more_vert,
            size: 20, color: ColorManager.myOrangeColor), // Smaller icon size
        padding: EdgeInsets.zero, // Remove default padding
        onSelected: (value) {
          if (value == 'logout') {
            _showLogoutConfirmation(context);
          }
          if (value == 'profile') {
            Navigator.pushNamed(context, '/profile');
          }
        },
        itemBuilder: (BuildContext context) {
          return [
            PopupMenuItem(
              value: 'profile',
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('Profile'.tr()),
                  const SizedBox(width: 10),
                  const Icon(Icons.person, color: ColorManager.myOrangeColor),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'logout',
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('Logout'.tr()),
                  const Icon(Icons.logout, color: ColorManager.myOrangeColor),
                ],
              ),
            ),
          ];
        },
      ),
    );
  }
}
