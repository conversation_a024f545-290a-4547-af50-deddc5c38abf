import 'dart:convert';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:qr_code_scanner_plus/qr_code_scanner_plus.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gym_app/core/constants/color_manager.dart';
import 'package:gym_app/core/helper/cache_helper.dart';
import 'package:gym_app/core/helper/dio_helper.dart';

class QRScanFAB extends StatefulWidget {
  final Function(String) onScanSuccess; // callback function for successful scan
  final GlobalKey qrKey;
  final Color buttonColor;
  final Color iconColor;
  final bool isEnabled;

  const QRScanFAB({
    super.key,
    required this.onScanSuccess,
    required this.qrKey,
    required this.buttonColor,
    required this.iconColor,
    required this.isEnabled,
  });

  @override
  _QRScanFABState createState() => _QRScanFABState();
}

class _QRScanFABState extends State<QRScanFAB> {
  QRViewController? controller;
  bool _isScanning = true;
  Barcode? result;
  int? _selectedSubscriptionID;

  @override
  void initState() {
    super.initState();
    DioHelper.init();
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 80,
      width: 80,
      child: FloatingActionButton(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(50)),
        onPressed: widget.isEnabled ? () => _onFABPressed(context) : null,
        backgroundColor: widget.isEnabled ? widget.buttonColor : Colors.grey,
        child: Icon(
          Icons.qr_code,
          color: widget.isEnabled ? widget.iconColor : Colors.grey,
          size: 40,
        ),
      ),
    );
  }

  void _onFABPressed123123123(BuildContext context) async {
    var entryID = CacheHelper.getData(key: 'studentEntryID') ?? 0;
    if (entryID == null) {
      CacheHelper.setData(key: 'studentEntryID', value: 0);
      entryID = 0;
    }

    if (entryID > 0) {
      _selectedSubscriptionID = -1;
      _showQRScanDialog(context);
    } else {
      try {
        Response response = await DioHelper.getData(
          url: '/api/StudentsEntries/BringSubscriptionsForEntryStudent',
        );
        if (response.statusCode == 200 &&
            json.decode(response.toString())['message'] == "Get succeeded") {
          List<dynamic> result = json.decode(response.toString())['result'];
          List<SubscriptionItem> subscriptions =
              result.map((json) => SubscriptionItem.fromJson(json)).toList();

          if (subscriptions.isEmpty) {
            _showPopup(context, 'NoActiveSubscriptions'.tr(), 'AskManger'.tr());
          } else {
            // Show a dialog to select one
            _showSubscriptionSelectionDialog(context, subscriptions);
          }
        } else {
          // Handle error
          // _showPopup(context, 'Error', 'Failed to fetch subscriptions');
        }
      } catch (e) {
        // _showPopup(context, 'Error', 'Error fetching subscriptions: $e');
      }
    }
  }

  void _onFABPressed(BuildContext context) async {
    var entryID = CacheHelper.getData(key: 'studentEntryID') ?? 0;
    if (entryID == null) {
      CacheHelper.setData(key: 'studentEntryID', value: 0);
      entryID = 0;
    }

    if (entryID > 0) {
      _selectedSubscriptionID = -1;
      _showQRScanDialog(context);
    } else {
      try {
        Response response = await DioHelper.getData(
          url: '/api/StudentsEntries/BringSubscriptionsForEntryStudent',
        );
        if (response.statusCode == 200 &&
            json.decode(response.toString())['message'] == "Get succeeded") {
          List<dynamic> result = json.decode(response.toString())['result'];
          List<SubscriptionItem> subscriptions =
              result.map((json) => SubscriptionItem.fromJson(json)).toList();

          if (subscriptions.isEmpty) {
            _showPopup(context, 'NoActiveSubscriptions'.tr(), 'AskManger'.tr());
          } else if (subscriptions.length == 1) {
            // automatically select the only subscription
            setState(() {
              _selectedSubscriptionID = subscriptions[0].subscriptionID;
            });
            _showQRScanDialog(context); // Proceed to scan QR code
          } else {
            _showSubscriptionSelectionDialog(context, subscriptions);
          }
        } else {
          // Handle error
          // _showPopup(context, 'Error', 'Failed to fetch subscriptions');
        }
      } catch (e) {
        // _showPopup(context, 'Error', 'Error fetching subscriptions: $e');
      }
    }
  }

  void _showSubscriptionSelectionDialog(
      BuildContext context, List<SubscriptionItem> subscriptions) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        int? selectedSubscriptionID = _selectedSubscriptionID;
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
          child: AlertDialog(
            backgroundColor: ColorManager.myDialogBackgroundColor,
            contentPadding: const EdgeInsets.all(16.0),
            title: Text(
              'SelectSubscription'.tr(),
              style: const TextStyle(fontSize: 20.0, color: Colors.white),
              textAlign: TextAlign.center,
            ),
            content: StatefulBuilder(
              builder: (BuildContext context, StateSetter setState) {
                return SizedBox(
                  width: double.maxFinite,
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: subscriptions.length,
                    itemBuilder: (context, index) {
                      bool isSelected = selectedSubscriptionID ==
                          subscriptions[index].subscriptionID;
                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            selectedSubscriptionID =
                                subscriptions[index].subscriptionID;
                          });
                        },
                        child: Container(
                          margin: const EdgeInsets.symmetric(vertical: 5),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? ColorManager.myInfoDialogButtonColor
                                : ColorManager.myDialogBackgroundColor,
                            border: Border.all(
                              color: isSelected ? Colors.black : Colors.grey,
                              width: isSelected ? 2 : 1,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  subscriptions[index].name,
                                  style: const TextStyle(color: Colors.white),
                                ),
                              ),
                              if (isSelected)
                                Icon(
                                  Icons.check_circle,
                                  color: Colors.black,
                                ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                );
              },
            ),
            actions: <Widget>[
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(); // Close the dialog
                },
                child: Text('Close'.tr(),
                    style: const TextStyle(color: Colors.white)),
              ),
              TextButton(
                onPressed: () {
                  if (selectedSubscriptionID != null) {
                    setState(() {
                      _selectedSubscriptionID = selectedSubscriptionID;
                    });
                    Navigator.of(context).pop(); // Close the dialog
                    _showQRScanDialog(context); // Proceed to scan QR code
                  } else {
                    // Show message to select a subscription
                    _showPopup(context, 'ErrorOccurred'.tr(),
                        'PleaseSelectSubscription'.tr());
                  }
                },
                child: Text('Approve'.tr(),
                    style: const TextStyle(color: Colors.white)),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showQRScanDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
          child: Container(
            height: 300.0,
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                Text(
                  'ScanQRCode'.tr(),
                  style: const TextStyle(
                      fontSize: 20.0,
                      fontWeight: FontWeight.bold,
                      color: Colors.white),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20.0),
                Expanded(
                  child: QRView(
                    key: widget.qrKey,
                    onQRViewCreated: _onQRViewCreated,
                  ),
                ),
                const SizedBox(height: 20.0),
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: Text(
                    'Close'.tr(),
                    style: const TextStyle(fontSize: 26, color: Colors.white),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    ).then((_) {
      setState(() {
        _isScanning = true; // allow re-scanning after the dialog is closed
      });
    });
  }

  void _onQRViewCreated(QRViewController controller) {
    this.controller = controller;
    controller.scannedDataStream.listen((scanData) async {
      if (mounted && _isScanning) {
        setState(() {
          result = scanData;
          _isScanning = false; // Stop further scanning
        });

        if (result != null && result!.code != null) {
          String qrCodeString = result!.code!;
          print('Scanned QR Code: $qrCodeString');

          // Pause the camera to prevent further scans
          await controller.pauseCamera();

          var jsonResponse;

          try {
            var value = int.tryParse(qrCodeString);
            if (value == null || value > 2147483647) {
              // if the number is bigger than INT
              _showPopup(context, 'TitleErrorScanningQrCode'.tr(),
                  'ErrorScanningQrCode'.tr());
              setState(() {
                _isScanning = true; // Allow re-scanning
              });
              return;
            }

            var entryID = CacheHelper.getData(key: 'studentEntryID') ?? 0;
            if (entryID == null) {
              CacheHelper.setData(key: 'studentEntryID', value: 0);
              entryID = 0;
            }
            Response response;

            if (entryID > 0) {
              // Exiting; send SubscriptionID as -1
              response = await DioHelper.postData(
                url: '/api/StudentsEntries/InsertStudentEntry',
                data: {
                  'studentEntryID': entryID,
                  'Barcode': int.parse(qrCodeString),
                  'SubscriptionID': -1, // Send -1 when exiting
                },
              );

              if (response.statusCode == 200 &&
                  json.decode(response.toString())['message'] == "Succeeded") {
                CacheHelper.setData(key: 'studentEntryID', value: 0);
              }
            } else {
              // Entering; use selected SubscriptionID
              response = await DioHelper.postData(
                url: '/api/StudentsEntries/InsertStudentEntry',
                data: {
                  'studentEntryID': 0,
                  'Barcode': int.parse(qrCodeString),
                  'SubscriptionID': _selectedSubscriptionID,
                },
              );
              if (response.statusCode == 200 &&
                  json.decode(response.toString())['message'] == "Succeeded") {
                var jsonResponse2 = json.decode(response.toString());
                var studentEntryID1 = jsonResponse2['result']['studentEntryID'];
                CacheHelper.setData(
                    key: 'studentEntryID', value: studentEntryID1);
              }
            }

            if (mounted) {
              if (response.statusCode == 200) {
                jsonResponse = json.decode(response.toString());

                if (jsonResponse['message'] == "Succeeded") {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('VerifiedSuccessfully'.tr())),
                  );
                  // Toggle the state and store it using CacheHelper
                  _toggleScanState();
                  widget.onScanSuccess(qrCodeString);
                  Navigator.pop(context); // Close the QR scan dialog

                  _showPopupSuccess();
                } else {
                  // Show error message and prevent any cache changes
                  Locale? currentLocal =
                      EasyLocalization.of(context)!.currentLocale;
                  if (currentLocal == const Locale('he', 'DZ')) {
                    _showPopup(context, 'ErrorOccurred'.tr(),
                        jsonResponse['result']['failedMessage1']);
                  } else {
                    _showPopup(context, 'ErrorOccurred'.tr(),
                        jsonResponse['result']['failedMessage2']);
                  }
                }
              } else if (response.statusCode == 202) {
                // 202 - admin already done exited for the user
                _toggleScanState();
                widget.onScanSuccess(qrCodeString);
                Navigator.pop(context); // Close the QR scan dialog

                jsonResponse = json.decode(response.toString());
                if (response.statusCode == 202) {
                  Locale? currentLocal =
                      EasyLocalization.of(context)!.currentLocale;
                  if (currentLocal == const Locale('he', 'DZ')) {
                    _showPopup(context, 'ErrorOccurred'.tr(),
                        jsonResponse['result']['failedMessage1']);
                  } else {
                    _showPopup(context, 'ErrorOccurred'.tr(),
                        jsonResponse['result']['failedMessage2']);
                  }
                  CacheHelper.setData(key: 'studentEntryID', value: 0);
                } else {
                  print("Unexpected status code: ${response.statusCode}");
                  // _showPopup(
                  //     context, 'Error', 'Unexpected response status code');
                }
              } else if (response.statusCode == 208) {
                // 208 - admin already done enter for the user

                _toggleScanState();
                widget.onScanSuccess(qrCodeString);
                Navigator.pop(context); // Close the QR scan dialog

                jsonResponse = json.decode(response.toString());
                if (response.statusCode == 208) {
                  var jsonResponse2 = json.decode(response.toString());
                  var studentEntryID1 =
                      jsonResponse2['result']['studentEntryID'];
                  CacheHelper.setData(
                      key: 'studentEntryID', value: studentEntryID1);
                  Locale? currentLocal =
                      EasyLocalization.of(context)!.currentLocale;
                  if (currentLocal == const Locale('he', 'DZ')) {
                    _showPopup(context, 'ErrorOccurred'.tr(),
                        jsonResponse['result']['error']['failedMessage1']);
                  } else {
                    _showPopup(context, 'ErrorOccurred'.tr(),
                        jsonResponse['result']['error']['failedMessage2']);
                  }
                  // CacheHelper.setData(key: 'studentEntryID', value: 0);
                } else {
                  print("Unexpected status code: ${response.statusCode}");
                  // _showPopup(
                  //     context, 'Error', 'Unexpected response status code');
                }
              } else if (response.statusCode == 203) {
                Navigator.pop(context);
                jsonResponse = json.decode(response.toString());
                if (response.statusCode == 203) {
                  Locale? currentLocal =
                      EasyLocalization.of(context)!.currentLocale;
                  if (currentLocal == const Locale('he', 'DZ')) {
                    _showPopup(context, 'ErrorOccurred'.tr(),
                        jsonResponse['result']['failedMessage1']);
                  } else {
                    _showPopup(context, 'ErrorOccurred'.tr(),
                        jsonResponse['result']['failedMessage2']);
                  }
                  CacheHelper.setData(key: 'studentEntryID', value: 0);
                } else {
                  print("Unexpected status code: ${response.statusCode}");
                  // _showPopup(
                  //     context, 'Error', 'Unexpected response status code');
                }
              }
            }
          } catch (e) {
            // Show error message and prevent any cache changes
            print("Error during scanning: $e");
            // _showPopup(context, 'Error', 'Error: $e');
          }

          setState(() {
            _isScanning = true; // Allow re-scanning
          });
        }
      }
    });
  }

  void _showPopupSuccess() {
    var scanState = CacheHelper.getData(key: 'scanState') as String?;
    scanState = (scanState == 'entered') ? 'exit' : 'entered';

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
          child: AlertDialog(
            backgroundColor: ColorManager.myDialogBackgroundColor,
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(3),
                      margin: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                      decoration: BoxDecoration(
                        color: ColorManager.myGreenDialogButtonColor,
                        borderRadius: BorderRadius.circular(100),
                      ),
                      child: const Icon(Icons.check),
                    ),
                    (scanState == 'entered')
                        ? Text('ExitSuccessfully'.tr(),
                            style: const TextStyle(
                                color: Colors.white, fontSize: 20))
                        : Text('EnteredSuccessfully'.tr(),
                            style: const TextStyle(
                                color: Colors.white, fontSize: 20)),
                  ],
                ),
                const SizedBox(height: 20),
                if (scanState == 'entered')
                  Text(
                    'GoodByeStatement'.tr(),
                    style: const TextStyle(color: Colors.white, fontSize: 16),
                  )
                else
                  Text(
                    'HelloStatement'.tr(),
                    style: const TextStyle(color: Colors.white, fontSize: 16),
                  ),
              ],
            ),
            actions: <Widget>[
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text('Close'.tr(),
                    style: const TextStyle(color: Colors.white)),
              ),
            ],
          ),
        );
      },
    );
  }

  void _toggleScanState() async {
    String? currentState = CacheHelper.getData(key: 'scanState') as String?;
    if (currentState == null || currentState == 'exit') {
      currentState = 'entered';
    } else {
      currentState = 'exit';
    }
    await CacheHelper.setData(key: 'scanState', value: currentState);
    setState(() {});
  }
}

void _showPopup(BuildContext context, String title, String message) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: AlertDialog(
          backgroundColor: ColorManager.myDialogBackgroundColor,
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(3),
                margin: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                decoration: BoxDecoration(
                  color: ColorManager.myRedDialogButtonColor,
                  borderRadius: BorderRadius.circular(100),
                ),
                child: const Icon(Icons.error, color: Colors.white),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
          content: Text(
            message,
            style: const TextStyle(color: Colors.white),
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Close'.tr(),
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      );
    },
  );
}

class SubscriptionItem {
  final int subscriptionID;
  final String name;

  SubscriptionItem({required this.subscriptionID, required this.name});

  factory SubscriptionItem.fromJson(Map<String, dynamic> json) {
    return SubscriptionItem(
      subscriptionID: json['subscriptionID'],
      name: json['name'],
    );
  }
}
