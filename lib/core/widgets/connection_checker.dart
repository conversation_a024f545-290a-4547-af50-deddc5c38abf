import 'dart:async';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

class ConnectivityService {
  final StreamController<bool> _connectionStatusController =
      StreamController<bool>.broadcast();

  ConnectivityService() {
    InternetConnection().onStatusChange.listen((status) {
      _connectionStatusController.add(status == InternetStatus.connected);
    });
  }

  Stream<bool> get connectionStatus => _connectionStatusController.stream;

  void dispose() {
    _connectionStatusController.close();
  }
}
