import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gym_app/core/constants/color_manager.dart';
import 'package:gym_app/core/constants/strings.dart';
import 'package:gym_app/core/widgets/localization_checker.dart';

class LanguageChangeButton extends StatefulWidget {
  final bool hideDecoration;

  const LanguageChangeButton({super.key, this.hideDecoration = false});

  @override
  _LanguageChangeButtonState createState() => _LanguageChangeButtonState();
}

class _LanguageChangeButtonState extends State<LanguageChangeButton> {
  @override
  Widget build(BuildContext context) {

    return Tooltip(
      message: "ChangeLanguage".tr(),
      child: GestureDetector(
        onTap: () {
          LocalizationCheacker.changeLanguge(context);
          setState(() {});  
        },
        child: Container(
          margin: const EdgeInsets.fromLTRB(5, 0, 0, 0),
          padding: const EdgeInsets.all(5),
          decoration: widget.hideDecoration
              ? null // If true, no decoration will be applied
              : BoxDecoration(
                  color: const Color(0xff323232),
                  borderRadius: BorderRadius.circular(50),
                ),
          child: SvgPicture.string(
            height: 24, // Set consistent icon size
            width: 24, // Ensure the width is also consistent
            Strings.translateIcon,
            color: ColorManager.myOrangeColor,
          ),
        ),
      ),
    );
  }
}
