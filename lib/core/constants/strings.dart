import 'package:flutter/foundation.dart' show kReleaseMode;
import '../../config/config_development.dart' as devConfig;
import '../../config/config_production.dart' as prodConfig;

class Strings {
  /*  mohammed connections */
  // static const String baseUrl =  '192.168.15.223:5000';
  // static const String baseUrl = '192.168.113.150:5000';
  // static const String baseUrl = '192.168.67.167:5214';
  //static const String baseUrl = '172.16.0.2:5214';
  
   // Choose the base URL based on the build mode
  static String get baseUrl => kReleaseMode ? prodConfig.Config.baseUrl : devConfig.Config.baseUrl;

  static const String translateIcon = '''
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418" />
</svg>
''';
// static const String messageIcon = '''
// <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6">
//   <path d="M1.5 8.67v8.58a3 3 0 0 0 3 3h15a3 3 0 0 0 3-3V8.67l-8.928 5.493a3 3 0 0 1-3.144 0L1.5 8.67Z" />
//   <path d="M22.5 6.908V6.75a3 3 0 0 0-3-3h-15a3 3 0 0 0-3 3v.158l9.714 5.978a1.5 1.5 0 0 0 1.572 0L22.5 6.908Z" />
// </svg>
// ''';

  static const String messageIcon = '''
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75" />
</svg>
''';

  static const String kickboxing = 'assets/images/Courses/2.jpg';
  static const String img_2 = 'assets/images/Courses/1.jpg';
  static const String img_3 = 'assets/images/Courses/2.jpg';

  static const String dancing = 'assets/images/Courses/03_Dancing.jpg';
  static const String running = 'assets/images/Courses/04_Running.jpg';
  static const String cycling = 'assets/images/Courses/05_Cycling.jpg';
  static const String pilates = 'assets/images/Courses/06_Pilates.jpg';
}
// <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="100" height="100" viewBox="0 0 100 100">
// <path d="M 18 13 C 15.243 13 13 15.243 13 18 L 13 69 C 13 71.757 15.243 74 18 74 L 49.314453 74 L 54.058594 87.335938 C 54.200594 87.733938 54.577 88 55 88 L 84 88 C 86.757 88 89 85.757 89 83 L 89 32 C 89 29.243 86.757 27 84 27 L 51.466797 27 L 46.947266 13.679688 C 46.809266 13.273688 46.428 13 46 13 L 18 13 z M 18 15 L 45.144531 15 L 51.662109 33.904297 C 51.734109 34.111297 51.926766 34.242188 52.134766 34.242188 C 52.188766 34.242187 52.244828 34.232844 52.298828 34.214844 C 52.559828 34.124844 52.697422 33.839125 52.607422 33.578125 L 51.029297 29 L 84 29 C 85.654 29 87 30.346 87 32 L 87 83 C 87 84.654 85.654 86 84 86 L 56.341797 86 L 59.5 81.638672 C 59.662 81.415672 59.612672 81.101453 59.388672 80.939453 C 59.163672 80.778453 58.852406 80.826734 58.691406 81.052734 L 55.507812 85.445312 L 51.082031 73 L 64.521484 73 L 61.398438 77.310547 C 61.236438 77.533547 61.285766 77.847766 61.509766 78.009766 C 61.733766 78.168766 62.045031 78.121484 62.207031 77.896484 L 65.904297 72.792969 C 65.91025 72.784763 65.908662 72.774089 65.914062 72.765625 C 65.993659 72.641209 66.024502 72.488243 65.972656 72.337891 L 63.984375 66.574219 C 65.467518 65.513564 66.896884 64.314722 68.238281 62.988281 C 70.813281 65.726281 73.854156 68.447937 77.285156 71.085938 C 77.373156 71.153937 77.479844 71.189453 77.589844 71.189453 C 77.611844 71.189453 77.632297 71.188547 77.654297 71.185547 C 77.786297 71.168547 77.905328 71.097188 77.986328 70.992188 L 79.806641 68.613281 C 79.974641 68.394281 79.933844 68.082063 79.714844 67.914062 C 76.402844 65.366062 73.453406 62.704047 70.941406 59.998047 C 73.318406 57.012047 75.898047 52.293 77.498047 48 L 82 48 C 82.276 48 82.5 47.776 82.5 47.5 L 82.5 44.5 C 82.5 44.224 82.276 44 82 44 L 70.5 44 L 70.5 41.5 C 70.5 41.224 70.276 41 70 41 L 67 41 C 66.724 41 66.5 41.224 66.5 41.5 L 66.5 44 L 56.199219 44 L 53.962891 37.517578 C 53.871891 37.256578 53.587172 37.116031 53.326172 37.207031 C 53.065172 37.297031 52.927578 37.58175 53.017578 37.84375 L 55.367188 44.652344 C 55.368215 44.655513 55.366094 44.658953 55.367188 44.662109 L 55.898438 46.195312 L 61.884766 63.550781 C 61.885996 63.554652 61.885389 63.558643 61.886719 63.5625 L 62.917969 66.552734 C 62.918941 66.555554 62.920855 66.557751 62.921875 66.560547 L 64.798828 72 L 50.019531 72 L 49.5 72 L 18 72 C 16.346 72 15 70.654 15 69 L 15 18 C 15 16.346 16.346 15 18 15 z M 34.050781 30.150391 C 33.840781 30.150391 33.660078 30.280938 33.580078 30.460938 L 24.039062 54.089844 C 23.979063 54.239844 23.999844 54.410781 24.089844 54.550781 C 24.179844 54.690781 24.339766 54.769531 24.509766 54.769531 L 27.890625 54.769531 C 28.100625 54.769531 28.279375 54.639219 28.359375 54.449219 L 30.400391 49.060547 L 40.900391 49.060547 L 42.980469 54.449219 C 43.050469 54.649219 43.239453 54.769531 43.439453 54.769531 L 46.830078 54.769531 C 46.990078 54.769531 47.150234 54.690781 47.240234 54.550781 C 47.330234 54.410781 47.349062 54.239844 47.289062 54.089844 L 37.75 30.460938 C 37.67 30.280938 37.489062 30.150391 37.289062 30.150391 L 34.050781 30.150391 z M 34.380859 31.150391 L 36.949219 31.150391 L 46.089844 53.769531 L 43.789062 53.769531 L 41.710938 48.380859 C 41.640937 48.190859 41.45 48.060547 41.25 48.060547 L 30.050781 48.060547 C 29.840781 48.060547 29.660078 48.190859 29.580078 48.380859 L 27.539062 53.769531 L 25.25 53.769531 L 34.380859 31.150391 z M 35.669922 33.160156 C 35.569922 33.160156 35.470469 33.185625 35.386719 33.234375 C 35.302969 33.283125 35.234219 33.354219 35.199219 33.449219 L 30.589844 45.759766 C 30.559844 45.839766 30.550547 45.923203 30.560547 46.001953 C 30.570547 46.080703 30.600391 46.155703 30.650391 46.220703 C 30.695391 46.290703 30.754219 46.345312 30.824219 46.382812 C 30.894219 46.420313 30.975547 46.439453 31.060547 46.439453 L 40.279297 46.439453 C 40.439297 46.439453 40.589453 46.360703 40.689453 46.220703 C 40.779453 46.090703 40.800234 45.919766 40.740234 45.759766 L 36.130859 33.449219 C 36.060859 33.259219 35.859922 33.160156 35.669922 33.160156 z M 35.669922 35.060547 L 39.550781 45.439453 L 31.779297 45.439453 L 35.669922 35.060547 z M 67.5 42 L 69.5 42 L 69.5 44.5 C 69.5 44.776 69.724 45 70 45 L 81.5 45 L 81.5 47 L 77.150391 47 C 76.939391 47 76.750688 47.132078 76.679688 47.330078 C 75.065687 51.778078 72.339531 56.750172 69.894531 59.701172 C 69.735531 59.893172 69.742156 60.173422 69.910156 60.357422 C 72.425156 63.107422 75.383938 65.812344 78.710938 68.402344 L 77.498047 69.986328 C 74.117047 67.356328 71.130188 64.648687 68.617188 61.929688 C 68.525187 61.830688 68.394813 61.771531 68.257812 61.769531 L 68.25 61.769531 C 68.116 61.769531 67.986531 61.822922 67.894531 61.919922 C 66.559531 63.278922 65.133484 64.512703 63.646484 65.595703 L 62.951172 63.580078 C 64.214172 62.611078 65.444375 61.519078 66.609375 60.330078 C 66.788375 60.147078 66.800719 59.857156 66.636719 59.660156 C 63.575719 55.985156 62.215719 53.321453 61.761719 52.314453 L 63.638672 51.603516 C 64.101672 52.573516 65.339812 54.888625 67.882812 58.015625 C 67.977812 58.133625 68.121484 58.201172 68.271484 58.201172 C 68.422484 58.201172 68.563203 58.132625 68.658203 58.015625 C 71.117203 54.981625 73.047578 51.499063 74.392578 47.664062 C 74.446578 47.512062 74.422125 47.342937 74.328125 47.210938 C 74.234125 47.077938 74.081922 47 73.919922 47 L 57.234375 47 L 56.542969 45 L 67 45 C 67.276 45 67.5 44.776 67.5 44.5 L 67.5 42 z M 57.578125 47.998047 L 73.207031 47.998047 C 71.990031 51.259047 70.330578 54.249437 68.267578 56.898438 C 65.583578 53.507438 64.553656 51.227141 64.347656 50.744141 C 64.232656 50.515141 63.962656 50.414953 63.722656 50.501953 L 60.921875 51.5625 C 60.671875 51.6575 60.541 51.9325 60.625 52.1875 C 60.635 52.2175 61.696172 55.206219 65.576172 59.949219 C 64.621777 60.899368 63.621621 61.765153 62.601562 62.564453 L 57.578125 47.998047 z"></path>
// </svg>
// <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="100" height="100" viewBox="0 0 50 50">
// <path d="M 6 3 C 4.300781 3 3 4.300781 3 6 L 3 26 C 3 27.699219 4.300781 29 6 29 L 6.0625 29 L 10.46875 23.71875 L 10.78125 23.34375 C 10.476563 23.460938 10.273438 23.542969 10.21875 23.5625 L 9.59375 21.65625 C 9.648438 21.636719 12.394531 20.699219 15.0625 18.8125 C 12.503906 16.488281 11.207031 14.121094 11.125 13.96875 L 12.875 13.03125 C 12.894531 13.066406 14.167969 15.34375 16.65625 17.53125 C 18.265625 16.078125 19.625 14.230469 19.9375 12 L 8 12 L 8 10 L 16 10 L 16 8 L 18 8 L 18 10 L 25 10 L 25 12 L 21.9375 12 C 21.640625 14.789063 20.132813 17.035156 18.28125 18.78125 C 19.03125 19.300781 19.847656 19.777344 20.75 20.1875 C 21.617188 19.449219 22.742188 19 24 19 L 29 19 L 29 6 C 29 4.300781 27.699219 3 26 3 Z M 16.6875 20.125 C 15.246094 21.203125 13.75 22 12.5625 22.5625 L 13.53125 23.71875 L 17.9375 29 L 19 29 L 19 24 C 19 23.214844 19.1875 22.46875 19.5 21.8125 C 18.464844 21.308594 17.53125 20.742188 16.6875 20.125 Z M 24 21 C 22.300781 21 21 22.300781 21 24 L 21 32.0625 L 26.28125 36.46875 L 28.125 38 L 26.28125 39.53125 L 21 43.9375 L 21 44 C 21 45.699219 22.300781 47 24 47 L 44 47 C 45.699219 47 47 45.699219 47 44 L 47 24 C 47 22.300781 45.699219 21 44 21 Z M 12 25 L 7 31 L 10 31 L 10 35 L 14 35 L 14 31 L 17 31 Z M 33 26.40625 L 35.09375 26.40625 L 40.3125 40.1875 L 37.8125 40.1875 L 36.6875 37 L 31.40625 37 L 30.3125 40.1875 L 27.8125 40.1875 Z M 34 29.40625 L 32 35.09375 L 36 35.09375 Z M 19 33 L 19 36 L 10 36 L 14 40 L 19 40 L 19 43 L 25 38 Z"></path>
// </svg>
