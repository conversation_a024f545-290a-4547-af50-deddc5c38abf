import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gym_app/app.dart';
import 'package:gym_app/core/helper/cache_helper.dart';
import 'package:gym_app/core/helper/dio_helper.dart';
import 'package:gym_app/core/services/one_signal_service.dart';
import 'package:gym_app/features/home/<USER>/home_page.dart';
import 'dart:io';

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback = (X509Certificate cert, String host, int port) {
        return host == 'app.algym.co.il';
      };
  }
}
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();
  HttpOverrides.global = MyHttpOverrides();
  await CacheHelper.init(); // Initialize SharedPreferences
  await DioHelper.init();
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  await OneSignalNotificationService.initOneSignal();

  bool isLoggedIn = CacheHelper.getData(key: 'isLoggedIn') ?? false;

  // Check token validity and refresh if necessary
  if (isLoggedIn) {
    await _checkAndRefreshToken();
  }

  runApp(
    EasyLocalization(
      // supportedLocales: const [Locale('ar', 'AE'), Locale('ar', 'DZ')],
      supportedLocales: const [Locale('ar', 'AE'), Locale('he', 'DZ')],
      path:
          'assets/translations', // <-- change the path of the translation files
      fallbackLocale: const Locale('he', 'DZ'),
      child: MyApp(isLoggedIn: isLoggedIn),
    ),
  );
  // DependencyInjection.init();
}

Future<void> _checkAndRefreshToken() async {
  final token = CacheHelper.getData(key: 'token');
  final refreshToken = CacheHelper.getData(key: 'refresh_token');

  if (token == null || refreshToken == null) {
    // Handle case where tokens are missing
    return;
  }

  final isTokenExpired = await DioHelper.isTokenExpired(token);
  if (isTokenExpired) {
    await DioHelper.refreshToken();
  }
}
