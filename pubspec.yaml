name: gym_app
description: "A new Flutter project."

publish_to: 'none'

version: 1.0.25+25

environment:
  sdk: '>=3.4.0-190.0.dev <4.0.0'

dependencies:
  bloc: ^8.1.4
  camera: 0.10.5+9
  # connectivity_plus: 2.3.0
  cupertino_icons: ^1.0.6
  dio: ^5.6.0
  easy_localization: ^3.0.3
  flutter:
    sdk: flutter
  flutter_bloc: ^8.1.6
  flutter_secure_storage: ^9.2.2
  flutter_svg: ^2.0.10+1
  get: ^4.6.5
  internet_connection_checker_plus: ^2.5.1
  permission_handler: ^11.3.1
  pin_code_fields: ^8.0.1
  qr_code_scanner_plus: ^2.0.10+1
  shared_preferences: ^2.1.0
  onesignal_flutter: ^5.3.4
  win32: ^5.5.0
  http: any

  provider: any
  intl: any
dev_dependencies:
  flutter_launcher_icons: ^0.14.1
  flutter_lints: ^4.0.0
  flutter_test:
    sdk: flutter


flutter_launcher_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  image_path: "assets/images/app_icon.jpeg"
  adaptive_icon_background: "assets/images/app_icon.jpeg"
  adaptive_icon_foreground: "assets/images/app_icon.jpeg"
  adaptive_icon_foreground_inset: 16

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/translations/
    - assets/images/Courses/

  fonts:
    - family: IBM_Plex_Sans_Arabic
      fonts:
        - asset: assets/fonts/IBMPlexSansArabic-Regular.ttf
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
    - family: Heebo
      fonts:
        - asset: assets/fonts/Heebo-Regular.ttf